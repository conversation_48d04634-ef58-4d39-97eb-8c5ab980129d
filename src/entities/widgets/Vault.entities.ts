import { IAllowedZone } from 'entities/api/calculationsManager.entities.ts'
import { PlanningStage, PlanningStageRussia } from 'entities/shared/common.entities.ts'
import { DetailedSettings } from 'handsontable/plugins/nestedHeaders'

export enum MessagesWarnings {
  SET_FIXING_VALUES = 'SET_FIXING_VALUES',
  RESET_FIXING_VALUES = 'RESET_FIXING_VALUES',
  //
  LOAD_AVRCHM = 'LOAD_AVRCHM',
  UPDATE_VAULT = 'UPDATE_VAULT',
  DO_OPTIMIZATION = 'DO_OPTIMIZATION',
  CALCULATE_ALLOWED_ZONES = 'CALCULATE_ALLOWED_ZONES',
  CALCULATE_GENERATION = 'CALCULATE_GENERATION',
  CALCULATE_GENERATION_MAXIMUM = 'CALCULATE_GENERATION_MAXIMUM',
  ENTERING_ALLOWED_ZONES = 'ENTERING_ALLOWED_ZONES',
  ENTERING_ALLOWED_ZONES_TO_BOUNDS = 'ENTERING_ALLOWED_ZONES_TO_BOUNDS',
  BALANCE_RGU = 'BALANCE_RGU',
  WRITE_MODES = 'WRITE_MODES',
  LOAD_ALL = 'LOAD_ALL',
  LOAD_ISP = 'LOAD_ISP',
  LOAD_MODES = 'LOAD_MODES',
  LOAD_CM = 'LOAD_CM',
  INITIALIZE = 'INITIALIZE',
  UPDATE_ACCEPT = 'UPDATE_ACCEPT',
  UPDATE_ACCEPT_CANCEL = 'UPDATE_ACCEPT_CANCEL',
  //
  RESET = 'RESET',
  SAVE = 'SAVE',
  RESET_VAULT = 'RESET_VAULT',
  SAVE_VAULT = 'SAVE_VAULT',
  HISTORY_ACCEPT = 'HISTORY_ACCEPT',
  HISTORY_MODES = 'HISTORY_MODES',
  EXPORT_REPORT = 'EXPORT_REPORT',
  SEND_REPORT = 'SEND_REPORT',
}

export interface IRguCell {
  P_MIN_RESULT: string
  P_GEN: string | number
  P_MAX_RESULT: string

  RESERVES_MAX: string
  AVRCHM: string | number
  NPRCH: string | number

  P_MIN: string
  P_MAX: string

  CM_P_MIN: string | number
  CM_P_MAX: string | number

  MODES_P_MIN: string | number
  MODES_P_MAX: string | number
  MODES_DECLARED: string | number
}

export interface ITempGesCell {
  P_MIN_RESULT: number | string | null
  P_GEN: number | string | null
  P_MAX_RESULT: number | string | null
  RESERVES_MAX: number | string | null
  AVRCHM_LOAD: number | string | null
  NPRCH?: number | string | null // Только для станции
  P_MIN: number | string | null
  P_MAX: number | string | null
  CM_P_MIN: number | string | null | undefined
  CM_P_MAX: number | string | null | undefined
  MODES_P_MIN: number | string | null | undefined
  MODES_P_MAX: number | string | null | undefined
  MODES_DECLARED?: number | string | null // Только для cтанции
  CONSUMPT: number | string | null
  allowedZones: IAllowedZone[]
}

export interface ITempGaesCell {
  P_GEN: number
  P_SOURCE: number
  MODES_P_MIN: number
  MODES_P_MAX: number
  CONSUMPT: number
}

export type IRguCells = IRguCell[]

export interface IVaultNestedHeadersDetailSettings extends DetailedSettings {
  accepted?: boolean
  code?: PlanningStage
}

export type IVaultNestedHeaders = Array<Array<IVaultNestedHeadersDetailSettings>>

export interface ICustomHeader {
  col: number
  className: string
  stageClassName?: '' | 'planningStagesDoNotMatchHeaderLabel' | 'RSVHeaderLabel' | 'VSVGOHeaderLabel'
  code?: PlanningStage
  thText?: PlanningStageRussia
}

export type IVaultCustomHeaders = Array<ICustomHeader>
