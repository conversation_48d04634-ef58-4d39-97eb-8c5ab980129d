// @ts-nocheck
import { ClickAwayListener } from '@mui/material'
import { IAllowedZone, TCalcGenerationMethod } from 'entities/api/calculationsManager.entities.ts'
import { TIME_LOADER } from 'entities/constants.ts'
import {
  AbortRequestReason,
  CalculationColumn,
  CalculationTaskType,
  PlanningStageRussia,
  TaskStatus,
} from 'entities/shared/common.entities.ts'
import { ROLES } from 'entities/shared/roles.entities.ts'
import { ICalcModelPlantListItem } from 'entities/store/calcModelStore.entities.ts'
import { ICalculationsPageStore, StagesInitStatus } from 'entities/store/calculationPageStore.entities.ts'
import {
  ICustomHeader,
  IRguCells,
  ITempGesCell,
  IVaultCustomHeaders,
  IVaultNestedHeaders,
  MessagesWarnings,
} from 'entities/widgets/Vault.entities.ts'
import { ButtonMailingReport } from 'features/ButtonMailingReport'
import { TelemetryTable } from 'features/TelemetryTable'
import Handsontable from 'handsontable'
import { CellChange } from 'handsontable/common'
import { Settings as CollapsibleColumnsSettings } from 'handsontable/plugins/collapsibleColumns'
import { observer } from 'mobx-react'
import {
  getValueEminEmax,
  rguStationHeaderClassNameConfig,
  roundingTo3DigitsEminEmaxPgen,
  stationHeaderClassNameConfig,
  validateDailyOutputMax,
  validateDailyOutputMin,
  validateDailyOutputPlan,
} from 'pages/CalculationsPage/lib'
import { enrichStationDataBeforeSave } from 'pages/CalculationsPage/lib/spreadsheetHelpers.ts'
import { CalculationActionButton } from 'pages/CalculationsPage/ui/CalculationButton/CalculationActionButton.tsx'
import cls from 'pages/CalculationsPage/ui/CalculationsPage.module.scss'
import { ConfirmModalModalProps } from 'pages/CalculationsPage/ui/StationBody/entities'
import {
  ACTUAL_ITEM,
  additionalValidation,
  COLUMN_BLOCK_SIZES,
  COLUMN_POSITIONS,
  downloadTheSourceData,
  getColorStage,
  getGesCellPropByTableCoords,
  getPrepareDate,
  getRguColumnIndex,
  getStationColumnIndex,
  getStatusCell,
  getStyledComment,
  getValidGES,
  handleAcceptObject,
  handleDisacceptObject,
  handleSelectedCoords,
  keysGES,
  resize,
  updateManualAdjustmentStatusForCell,
} from 'pages/CalculationsPage/ui/StationBody/lib'
import { makePMax, makePMaxFormula } from 'pages/CalculationsPage/ui/StationBody/lib/makePMaxFormula'
import { destroyPMaxFormula } from 'pages/CalculationsPage/ui/StationBody/lib/makePMaxFormula/makePMaxFormula.ts'
import { makePMin, makePMinFormula } from 'pages/CalculationsPage/ui/StationBody/lib/makePMinFormula'
import { destroyPMinFormula } from 'pages/CalculationsPage/ui/StationBody/lib/makePMinFormula/makePMinFormula.ts'
import { AcceptErrorModal } from 'pages/CalculationsPage/ui/StationBody/ui/AcceptErrorModal'
import { AcceptModal } from 'pages/CalculationsPage/ui/StationBody/ui/AcceptModal'
import { ActualValuesChart } from 'pages/CalculationsPage/ui/StationBody/ui/ActualValuesChart'
import { ConfirmModal } from 'pages/CalculationsPage/ui/StationBody/ui/ConfirmModal'
import { ExportReport } from 'pages/CalculationsPage/ui/StationBody/ui/ExportReport'
import { HistoryAcceptModal } from 'pages/CalculationsPage/ui/StationBody/ui/HistoryAcceptModal'
import { ModalHistoryModes } from 'pages/CalculationsPage/ui/StationBody/ui/ModalHistoryModes/ModalHistoryModes.tsx'
import { ModalInit } from 'pages/CalculationsPage/ui/StationBody/ui/ModalInit'
import { PlannedValuesChart } from 'pages/CalculationsPage/ui/StationBody/ui/PlannedValuesChart'
import { StationHeader } from 'pages/CalculationsPage/ui/StationBody/ui/StationHeader'
import { Zones } from 'pages/CalculationsPage/ui/StationBody/ui/Zones'
import { Dispatch, FC, SetStateAction, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import api from 'shared/api'
import { calcCellFromAlphabet } from 'shared/lib/alphabet'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { locationParse } from 'shared/lib/locationParse'
import { round } from 'shared/lib/round'
import { validateDate } from 'shared/lib/validateDate'
import { AccessControl } from 'shared/ui/AccessControl'
import { Loader } from 'shared/ui/Loader'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { useStore } from 'stores/useStore.ts'
import { useSpreadsheetHelpers } from 'widgets/Spreadsheet/hooks'
import { calculateSpreadsheetValueByFormula, SpreadsheetSelectedCells } from 'widgets/Spreadsheet/ui/lib'
import {
  getMapOfNestedHeadersBasedOnConfig,
  MapOfNestedHeadersBasedOnConfig,
} from 'widgets/Spreadsheet/ui/lib/getMapOfNestedHeadersBasedOnConfig/getMapOfNestedHeadersBasedOnConfig.ts'
import { IInputResultItemProp, Spreadsheet } from 'widgets/Spreadsheet/ui/Spreadsheet.tsx'

import { StationAcceptButton } from './StationAcceptButton'
import { StationDisacceptButton } from './StationDisacceptButton'
import { StationSaveButton } from './StationSaveButton'

type CellSettings = Exclude<Handsontable.GridSettings['cell'], undefined>[0]
type ColumnSettings = Handsontable.ColumnSettings
type GridSettings = Handsontable.GridSettings

interface StationBodyProps {
  isEditRows: boolean
  setIsInputParams: Dispatch<SetStateAction<boolean>>
  editCellsUp: string[]
  setEditCellsUp: Dispatch<SetStateAction<string[]>>
}

export const StationBody: FC<StationBodyProps> = observer((props) => {
  const { isEditRows, editCellsUp, setEditCellsUp, setIsInputParams } = props
  const { calculationsPageStore, notificationStore, godModeStore } = useStore()
  const accessRole = [ROLES.TECHNOLOGIST]
  const {
    dataForStation,
    dataForStationOriginal,
    onDownloadTheSourceData,
    actualStage,
    calcAllowedZones,
    calcGeneration,
    calcEnteringAllowedZones,
    saveDataStation,
    loadDataForStation,
    date,
    isStagesExist,
    selectedStage,
    setAcceptObject,
    setDisacceptObject,
    stages,
    calcRGE,
    updateStation,
    acceptedErrors,
    resetAcceptErrors,
    inizCalc,
    selectLeftMenu,
    isFinishStage,
    isLoadingTelemetryInProcess,
    telemetryTableDate,
    getTelemetry,
    getTelemetryByDate,
    setTelemetryTableDate,
    isLoadingReportDailyOutput,
    setSyncStatus,
    isLastDay,
    editMode,
    plantsListForAside,
    setPlantsListForAside,
    telemetryTableData,
    setSelectedCellAllowedZones,
    refreshPlantsListVisuals,
  } = calculationsPageStore
  const { godMode } = godModeStore
  const initLoadDataAbortControllerRef = useRef<AbortController | null>(null)
  const { year = null, month = null, day = null } = locationParse(location.search)
  const nowDate = new Date()
  nowDate.setDate(nowDate.getDate() + 1)
  const initDate = useCallback(() => {
    return year && month && day ? new Date(`${year}-${month}-${day}`) : nowDate
  }, [year, month, day])
  const refUp = useRef<HTMLDivElement | null>(null)
  const refDown = useRef<HTMLDivElement | null>(null)
  const [widthUp, setWidthUp] = useState<number | undefined>()
  const [isHistoryModal, setIsHistoryModal] = useState(null)
  const [isAccepted, setIsAccepted] = useState<boolean | null>(null)
  const [columnUp, setColumnUp] = useState<ColumnSettings[]>([])
  const [isConfirmModal, setIsConfirmModal] = useState<ConfirmModalModalProps | null>(null)
  const [nestedHeadersUp, setNestedHeadersUp] = useState<IVaultNestedHeaders>([])
  const [collapsibleColumnsUp, setCollapsibleColumnsUp] = useState<CollapsibleColumnsSettings>([])
  const [dataUp, setDataUp] = useState<(string | number)[][]>([])
  const [cellUp, setCellUp] = useState<CellSettings[]>([])
  const [inputValues, setInputValues] = useState<any>(null)
  const [dateISP, setDateISP] = useState(new Date())
  const [signalUpdateChart, setSignalUpdateChart] = useState(false)
  const isMounted = useRef(true)
  const [isLoadingUp, setIsLoadingUp] = useState<boolean>(true)
  const [isLoadingDown, setIsLoadingDown] = useState<boolean>(true)
  const { beforeCancelChange } = useSpreadsheetHelpers()
  const heightUp = dataForStation.rgus.length > 0 ? 496 : 478

  useHotkeys('ctrl+shift+s', () => !viewOnly && isEditRows && !isLastDay && !dataForStation?.accepted && save(), {
    enableOnFormTags: true,
  })
  useHotkeys('ctrl+shift+x', () => !viewOnly && isEditRows && !isLastDay && !dataForStation?.accepted && resetData(), {
    enableOnFormTags: true,
  })

  useEffect(() => {
    if (isLoadingReportDailyOutput === false) {
      loadData()
    }
  }, [isLoadingReportDailyOutput])

  useEffect(
    () => () => {
      isMounted.current = false
      resetData()
    },
    [],
  )

  useEffect(() => {
    if (refUp && refDown) {
      setEditCellsUp([])
      setIsInputParams(false)
    }
  }, [refUp, selectLeftMenu, refDown])

  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      resize(refUp, () => {}, setWidthUp)
    })
    if (refUp.current !== null) {
      resizeObserver.observe(refUp.current)
    }

    return () => {
      refUp.current && resizeObserver.unobserve(refUp.current)
    }
  }, [refUp, refDown])

  useEffect(() => {
    // Обновляем компонент таблиц после переключения на новую дату
    setIsLoadingUp(true)
    setIsLoadingDown(true)
  }, [date, selectedStage])

  const viewOnly = plantsListForAside.find((el) => el.value === selectLeftMenu)?.viewOnly || isFinishStage

  const afterChangeUp = useCallback(
    (changes: CellChange[] | null) => {
      if (changes) {
        changes?.forEach(([row, prop, _, newValue]) => {
          //calculate
          const indexAvrchm = getStationColumnIndex(
            'RESERVES',
            COLUMN_POSITIONS.AVRCHM_LOAD,
            dataForStation.rgus.length,
          )
          const rguIdxs = dataForStation.rgus.map((_, idx) =>
            getRguColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, idx, dataForStation.rgus.length),
          )
          const isAvrchm = indexAvrchm === prop
          const FLOOD_MODE_WATCH = inputValues?.FLOOD_MODE_WATCH ?? false
          const REGULATED_UNIT = dataForStation.REGULATED_UNIT ?? 'PLANT'
          const RGU_MIN = dataForStation.rgus.map((_, index) => {
            return getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MIN_RESULT, index, dataForStation.rgus.length)
          })
          const RGU_MAX = dataForStation.rgus.map((_, index) => {
            return getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MAX_RESULT, index, dataForStation.rgus.length)
          })
          if (isAvrchm) {
            setDataUp((prev) => {
              return prev.map((itemRow, index) => {
                if (index === row) {
                  return itemRow.map((itemCell, indexx) => {
                    const isFindPMin = RGU_MIN.some((el) => el === indexx)
                    const isFindPMax = RGU_MAX.some((el) => el === indexx)
                    if (isFindPMin) {
                      return destroyPMinFormula(itemCell)
                    }
                    if (isFindPMax) {
                      return destroyPMaxFormula(itemCell)
                    }
                    if (indexx === 0) {
                      if (FLOOD_MODE_WATCH) {
                        if (REGULATED_UNIT === 'PLANT') {
                          const pMaxResultColumnIndex = getStationColumnIndex(
                            'RESULTS',
                            COLUMN_POSITIONS.P_MAX_RESULT,
                            dataForStation.rgus.length,
                          )

                          return `=ROUND(MAX(0,${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1}) - 1,3)`
                        } else {
                          const rgusSum = dataForStation.rgus.map((_, key) => {
                            const pMinResultColumnIndex = getRguColumnIndex(
                              'RESULTS',
                              COLUMN_POSITIONS.P_MIN_RESULT,
                              key,
                              dataForStation.rgus.length,
                            )

                            return `${calcCellFromAlphabet(pMinResultColumnIndex + 1)}${index + 1}`
                          })
                          const rgusForPminString = rgusSum.join('+')

                          return `=ROUND(MAX(0,${rgusForPminString}),3)`
                        }
                      } else {
                        const pMinColumnIndex = getStationColumnIndex(
                          'LIMITS',
                          COLUMN_POSITIONS.P_MIN,
                          dataForStation.rgus.length,
                        )
                        const avrchmColumnIndex = getStationColumnIndex(
                          'RESERVES',
                          COLUMN_POSITIONS.AVRCHM_LOAD,
                          dataForStation.rgus.length,
                        )

                        return `=ROUND(MAX(0,${calcCellFromAlphabet(pMinColumnIndex + 1)}${index + 1}+${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}),3)`
                      }
                    }
                    if (indexx === 2) {
                      const pMaxColumnIndex = getStationColumnIndex(
                        'LIMITS',
                        COLUMN_POSITIONS.P_MAX,
                        dataForStation.rgus.length,
                      )
                      const avrchmColumnIndex = getStationColumnIndex(
                        'RESERVES',
                        COLUMN_POSITIONS.AVRCHM_LOAD,
                        dataForStation.rgus.length,
                      )
                      if (FLOOD_MODE_WATCH) {
                        if (REGULATED_UNIT === 'PLANT') {
                          return `=ROUND(MAX(0,${calcCellFromAlphabet(pMaxColumnIndex + 1)}${index + 1}-${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}),3)`
                        } else {
                          const rgusSum = dataForStation.rgus.map((_, key) => {
                            const pMaxResultColumnIndex = getRguColumnIndex(
                              'RESULTS',
                              COLUMN_POSITIONS.P_MAX_RESULT,
                              key,
                              dataForStation.rgus.length,
                            )

                            return `${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1}`
                          })
                          const rgusForPminString = rgusSum.join('+')

                          return `=ROUND(MAX(0,${rgusForPminString}),3)`
                        }
                      } else {
                        return `=ROUND(MAX(0,${calcCellFromAlphabet(pMaxColumnIndex + 1)}${index + 1}-${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}),3)`
                      }
                    }

                    return itemCell
                  })
                }

                return itemRow
              })
            })
          }
          const isAvrchmRgu = rguIdxs.includes(Number(prop))
          setCellUp((prev) => {
            const res = prev.map((el) => {
              if ((isAvrchm || isAvrchmRgu) && el.row === row && el.col === prop) {
                return { ...el, manual: true, allowedZones: [] }
              } else if (el.row === row && el.col === prop) {
                return { ...el, manual: true }
              } else if ((isAvrchm || isAvrchmRgu) && el.row === row) {
                return { ...el, allowedZones: [] }
              }

              return el
            })

            return res
          })
          // функция, которая отвечает за обновление ячеек таблицы
          updateDataStation(inputValues.FLOOD_MODE_WATCH, prop, row, newValue)
          //calculate
        })
        setSignalUpdateChart(true)
        const newEditCellsUp = changes.map(([row, prop]) => `${row}-${prop}`)
        setEditCellsUp(newEditCellsUp)
      }
    },
    [nestedHeadersUp, dataForStation, inputValues?.FLOOD_MODE_WATCH],
  )

  const [hotUp, setHotUp] = useState<any>(null)

  const validate = () => {
    if (!isLoadingUp) {
      const tableDataUp = hotUp?.getData()?.slice(0, 24) ?? []
      if (tableDataUp.length > 0) {
        let keysMap: string[] = []
        if (dataForStation.rgus.length > 0) {
          let one: string[] = []
          let two: string[] = []
          let three: string[] = []
          let four: string[] = []
          let five: string[] = []
          dataForStation.rgus.forEach((rgu) => {
            one = [...one, `${rgu.rguId}-P_MIN_RESULT`, `${rgu.rguId}-P_GEN`, `${rgu.rguId}-P_MAX_RESULT`]
            two = [...two, `${rgu.rguId}-RESERVES_MAX`, `${rgu.rguId}-AVRCHM_LOAD`, `${rgu.rguId}-NPRCH`]
            three = [...three, `${rgu.rguId}-P_MIN`, `${rgu.rguId}-P_MAX`]
            four = [...four, `${rgu.rguId}-CM_P_MIN`, `${rgu.rguId}-CM_P_MAX`]
            five = [...five, `${rgu.rguId}-MODES_P_MIN`, `${rgu.rguId}-MODES_P_MAX`, `${rgu.rguId}-MODES_DECLARED`]
          })
          keysMap = [
            'P_MIN_RESULT',
            'P_GEN',
            'P_MAX_RESULT',
            ...one,
            'RESERVES_MAX',
            'AVRCHM_LOAD',
            'NPRCH',
            ...two,
            'P_MIN',
            'P_MAX',
            ...three,
            'CM_P_MIN',
            'CM_P_MAX',
            ...four,
            'MODES_P_MIN',
            'MODES_P_MAX',
            'MODES_DECLARED',
            ...five,
            'CONSUMPT',
          ]
        } else {
          keysMap = [...keysGES, 'CONSUMPT']
        }
        setCellUp((prev) => {
          return prev.map((el) => {
            let object: ITempGesCell = {
              P_MIN_RESULT: 0,
              P_GEN: 0,
              P_MAX_RESULT: 0,
              RESERVES_MAX: 0,
              AVRCHM_LOAD: 0,
              NPRCH: 0,
              P_MIN: 0,
              P_MAX: 0,
              CM_P_MIN: 0,
              CM_P_MAX: 0,
              MODES_P_MIN: 0,
              MODES_P_MAX: 0,
              MODES_DECLARED: 0,
              CONSUMPT: 0,
              allowedZones: [],
            }
            const keys = keysMap[el?.col]
            if (keys) {
              const cols = keys?.split('-')
              let keyStation: string = ''
              if (cols.length === 1) {
                keyStation = cols[0]
                const P_GEN = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'P_GEN')])
                const cm_min = tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'CM_P_MIN')]
                const cm_max = tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'CM_P_MAX')]
                const modes_min = tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'MODES_P_MIN')]
                const modes_max = tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'MODES_P_MAX')]
                const modes_declared = tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'MODES_DECLARED')]
                const CM_P_MIN = cm_min === undefined || cm_min === '' || cm_min === null ? undefined : Number(cm_min)
                const CM_P_MAX = cm_max === undefined || cm_max === '' || cm_max === null ? undefined : Number(cm_max)
                const MODES_P_MIN = modes_min === undefined || modes_min === '' ? undefined : Number(modes_min)
                const MODES_P_MAX = modes_max === undefined || modes_max === '' ? undefined : Number(modes_max)
                const MODES_DECLARED =
                  modes_declared === undefined || modes_declared === '' ? undefined : Number(modes_declared)
                const P_MIN = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'P_MIN')])
                const P_MAX = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'P_MAX')])
                const AVRCHM_LOAD = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'AVRCHM_LOAD')])
                const NPRCH = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'NPRCH')])
                const CONSUMPT = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'CONSUMPT')])
                const P_MIN_RESULT = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'P_MIN_RESULT')])
                const P_MAX_RESULT = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'P_MAX_RESULT')])
                const RESERVES_MAX = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === 'RESERVES_MAX')])
                object = {
                  P_MIN_RESULT,
                  P_GEN,
                  P_MAX_RESULT,
                  RESERVES_MAX,
                  AVRCHM_LOAD,
                  NPRCH,
                  P_MIN,
                  P_MAX,
                  CM_P_MIN: CM_P_MIN === undefined ? undefined : CM_P_MIN,
                  CM_P_MAX: CM_P_MAX === undefined ? undefined : CM_P_MAX,
                  MODES_P_MIN: MODES_P_MIN === undefined ? undefined : MODES_P_MIN,
                  MODES_P_MAX: MODES_P_MAX === undefined ? undefined : MODES_P_MAX,
                  MODES_DECLARED: MODES_DECLARED === undefined ? undefined : MODES_DECLARED,
                  CONSUMPT,
                }
              } else {
                const [id, key] = cols
                keyStation = key
                const P_GEN = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-P_GEN`)])
                const cm_min = tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-CM_P_MIN`)]
                const cm_max = tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-CM_P_MAX`)]
                const modes_min = tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-MODES_P_MIN`)]
                const modes_max = tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-MODES_P_MAX`)]
                const modes_declared =
                  tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-MODES_DECLARED`)]
                const CM_P_MIN = cm_min === undefined || cm_min === '' || cm_min === null ? undefined : Number(cm_min)
                const CM_P_MAX = cm_max === undefined || cm_max === '' || cm_max === null ? undefined : Number(cm_max)
                const MODES_P_MIN = modes_min === undefined || modes_min === '' ? undefined : Number(modes_min)
                const MODES_P_MAX = modes_max === undefined || modes_max === '' ? undefined : Number(modes_max)
                const MODES_DECLARED =
                  modes_declared === undefined || modes_declared === '' ? undefined : Number(modes_declared)
                const P_MIN = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-P_MIN`)])
                const P_MAX = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-P_MAX`)])
                const AVRCHM_LOAD = Number(
                  tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-AVRCHM_LOAD`)],
                )
                const NPRCH = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-NPRCH`)])
                const CONSUMPT = Number(tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-CONSUMPT`)])
                const P_MIN_RESULT = Number(
                  tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-P_MIN_RESULT`)],
                )
                const P_MAX_RESULT = Number(
                  tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-P_MAX_RESULT`)],
                )
                const RESERVES_MAX = Number(
                  tableDataUp[el?.row][keysMap?.findIndex((item) => item === `${id}-RESERVES_MAX`)],
                )
                object = {
                  P_MIN_RESULT,
                  P_GEN,
                  P_MAX_RESULT,
                  RESERVES_MAX,
                  AVRCHM_LOAD,
                  NPRCH,
                  P_MIN,
                  P_MAX,
                  CM_P_MIN: CM_P_MIN === undefined ? undefined : CM_P_MIN,
                  CM_P_MAX: CM_P_MAX === undefined ? undefined : CM_P_MAX,
                  MODES_P_MIN: MODES_P_MIN === undefined ? undefined : MODES_P_MIN,
                  MODES_P_MAX: MODES_P_MAX === undefined ? undefined : MODES_P_MAX,
                  MODES_DECLARED: MODES_DECLARED === undefined ? undefined : MODES_DECLARED,
                  CONSUMPT,
                }
              }
              let isValid: boolean = false
              const value = object[keyStation]
              object['allowedZones'] = el?.allowedZones ?? []

              const P_GEN_RGU_ARR: number[] = []
              const AVRCHM_LOAD_ARR: number[] = []
              const P_MIN_ARR: number[] = []
              const P_MAX_ARR: number[] = []
              const CM_P_MIN_ARR: number[] = []
              const CM_P_MAX_ARR: number[] = []
              let P_GEN_PLANT = 0
              let AVRCHM_LOAD_PLANT = 0
              let P_MIN_PLANT = 0
              let P_MAX_PLANT = 0
              let CM_P_MIN_PLANT = 0
              let CM_P_MAX_PLANT = 0
              keysMap.forEach((elKey, index) => {
                const keys = elKey.split('-')
                const len = keys?.length ?? 1
                if (len === 1) {
                  const [key] = keys
                  const value = Number(tableDataUp[el?.row][index])
                  switch (key) {
                    case 'P_GEN':
                      P_GEN_PLANT = value
                      break
                    case 'AVRCHM_LOAD':
                      AVRCHM_LOAD_PLANT = value
                      break
                    case 'P_MIN':
                      P_MIN_PLANT = value
                      break
                    case 'P_MAX':
                      P_MAX_PLANT = value
                      break
                    case 'CM_P_MIN':
                      CM_P_MIN_PLANT = value
                      break
                    case 'CM_P_MAX':
                      CM_P_MAX_PLANT = value
                      break
                    default:
                      break
                  }
                } else {
                  const [_, key] = keys
                  const value =
                    tableDataUp[el?.row][index] !== undefined && tableDataUp[el?.row][index] !== ''
                      ? Number(tableDataUp[el?.row][index])
                      : undefined
                  if (key === 'P_GEN') {
                    P_GEN_RGU_ARR.push(value)
                  }
                  if (value !== undefined) {
                    switch (key) {
                      case 'AVRCHM_LOAD':
                        AVRCHM_LOAD_ARR.push(value)
                        break
                      case 'P_MIN':
                        P_MIN_ARR.push(value)
                        break
                      case 'P_MAX':
                        P_MAX_ARR.push(value)
                        break
                      case 'CM_P_MIN':
                        CM_P_MIN_ARR.push(value)
                        break
                      case 'CM_P_MAX':
                        CM_P_MAX_ARR.push(value)
                        break
                      default:
                        break
                    }
                  }
                }
              })
              const P_GEN_RGUS = P_GEN_RGU_ARR.reduce((acc, cur) => acc + (cur ?? 0), 0)
              const AVRCHM_LOAD_RGUS = AVRCHM_LOAD_ARR.reduce((acc, cur) => acc + cur, 0)
              const P_MIN_RGUS = P_MIN_ARR.reduce((acc, cur) => acc + cur, 0)
              const P_MAX_RGUS = P_MAX_ARR.reduce((acc, cur) => acc + cur, 0)
              const CM_P_MIN_RGUS = CM_P_MIN_ARR.reduce((acc, cur) => acc + cur, 0)
              const CM_P_MAX_RGUS = CM_P_MAX_ARR.reduce((acc, cur) => acc + cur, 0)

              const IS_VIEW_CM_P_MIN = CM_P_MIN_ARR?.length > 0
              const IS_VIEW_CM_P_MAX = CM_P_MAX_ARR?.length > 0
              const validObject = {
                is_Valid_P_GEN:
                  P_GEN_RGU_ARR.length > 0
                    ? Number(P_GEN_PLANT.toFixed(3)) !== Number(P_GEN_RGUS.toFixed(3))
                    : undefined,
                is_Valid_AVRCHM_LOAD:
                  AVRCHM_LOAD_ARR.length > 0
                    ? Number(AVRCHM_LOAD_PLANT.toFixed(3)) !== Number(AVRCHM_LOAD_RGUS.toFixed(3))
                    : undefined,
                is_Valid_P_MIN:
                  P_MIN_ARR.length > 0 ? Number(P_MIN_PLANT.toFixed(3)) < Number(P_MIN_RGUS.toFixed(3)) : undefined,
                is_Valid_P_MAX:
                  P_MAX_ARR.length > 0 ? Number(P_MAX_PLANT.toFixed(3)) > Number(P_MAX_RGUS.toFixed(3)) : undefined,
                is_Valid_CM_P_MIN: IS_VIEW_CM_P_MIN
                  ? Number(CM_P_MIN_PLANT !== undefined ? CM_P_MIN_PLANT.toFixed(3) : 0) <
                    Number(CM_P_MIN_RGUS !== undefined ? CM_P_MIN_RGUS.toFixed(3) : 0)
                  : undefined,
                is_Valid_CM_P_MAX: IS_VIEW_CM_P_MAX
                  ? Number(CM_P_MAX_PLANT !== undefined ? CM_P_MAX_PLANT.toFixed(3) : 0) >
                    Number(CM_P_MAX_RGUS !== undefined ? CM_P_MAX_RGUS.toFixed(3) : 0)
                  : undefined,
              }
              const countKeys = cols.length ?? 1
              const typeObj = countKeys === 1 ? 'PLANT' : 'RGU'
              const VALIDATE_MAIN = getValidGES(dataForStation.plantId, keyStation, object, value, inputResultProps[0])
              const VALIDATE_PLANT_WITH_RGU = additionalValidation(
                typeObj,
                object,
                value,
                keyStation,
                dataForStation.REGULATED_UNIT,
                validObject,
              )
              isValid = VALIDATE_MAIN === undefined && VALIDATE_PLANT_WITH_RGU === undefined
              const VALIDATE_MESSAGE_MAIN = VALIDATE_MAIN === undefined ? '' : VALIDATE_MAIN
              const VALIDATE_MESSAGE_PLANT_WITH_RGU =
                VALIDATE_PLANT_WITH_RGU === undefined ? '' : VALIDATE_PLANT_WITH_RGU
              const comment = VALIDATE_MESSAGE_MAIN + VALIDATE_MESSAGE_PLANT_WITH_RGU
              const editor =
                dataForStation.accepted ||
                (keyStation === 'AVRCHM_LOAD' &&
                  (inputValues?.FLOOD_MODE_WATCH || viewOnly || isFinishStage || !editMode)) ||
                keyStation === 'RESERVES_MAX'
                  ? false
                  : el.editor
              const renderer = getStatusCell(
                el.isMaxConsumptionHour,
                el.isMinConsumptionHour,
                editor,
                el.keyStation,
                el.plantOptimizedFinal,
                isValid,
                el?.manual || el?.fixed,
              )

              return {
                ...el,
                renderer,
                comment: isValid ? null : getStyledComment(comment),
              }
            } else {
              return el
            }
          })
        })
      }
    }
  }

  //validate
  useEffect(() => {
    if (dataForStation.plantId === selectLeftMenu) {
      validate()
    }
  }, [editCellsUp, inputValues, dataForStation.plantId, selectLeftMenu])
  //validate

  const [customHeadersUp, setCustomHeadersUp] = useState<ICustomHeader[]>([])

  //update data sockets
  useEffect(() => {
    if (updateStation !== null) {
      initLoadData()
    }
  }, [updateStation])
  //update data sockets

  useEffect(() => {
    if (inputValues !== null) {
      if (inputValues?.FLOOD_MODE_WATCH) {
        //INPUT VALUES
        const tableDataUp = hotUp?.getData()?.slice(0, 24) ?? []
        const getSUM_PMIN = () => {
          const res = tableDataUp.reduce(function (res: any, cur: any) {
            return res + cur[0]
          }, 0)

          return Number.isNaN(res) ? 0 : round(res / 1000, 3)
        }
        const getSUM_PMAX = () => {
          const res = tableDataUp.reduce(function (res: any, cur: any) {
            return res + cur[2]
          }, 0)

          return Number.isNaN(res) ? 0 : round(res / 1000, 3)
        }
        const SUM_PMIN = getSUM_PMIN()
        const SUM_PMAX = getSUM_PMAX()
        if (dataForStation.plantOptimized) {
          setInputValues((prev: any) => {
            return {
              ...prev,
              P_GEN_TARGET: undefined,
              W_MAX: round(SUM_PMAX, 3),
              W_MIN: round(SUM_PMIN, 3),
              FLOOD_MODE_WATCH: inputValues.FLOOD_MODE_WATCH,
            }
          })
        } else {
          setInputValues((prev: any) => {
            return {
              ...prev,
              W_MAX: undefined,
              W_MIN: undefined,
              P_GEN_TARGET: round(SUM_PMAX, 3),
              FLOOD_MODE_WATCH: inputValues.FLOOD_MODE_WATCH,
            }
          })
        }
        //INPUT VALUES
      } else {
        //INPUT VALUES
        setInputValues((prev: any) => {
          const inputValues = {
            P_GEN_TARGET: {
              value:
                String(prev['P_GEN_TARGET']).length === 0
                  ? ''
                  : roundingTo3DigitsEminEmaxPgen(Number(prev['P_GEN_TARGET'])),
            },
            W_MIN: {
              value: String(prev['W_MIN']).length === 0 ? '' : (round(Number(prev['W_MIN']) * 1000, 3) ?? 0),
            },
            W_MAX: {
              value: String(prev['W_MAX']).length === 0 ? '' : (round(Number(prev['W_MAX']) * 1000, 3) ?? 0),
            },
            // FLOOD_MODE_WATCH: {prev["FLOOD_MODE_WATCH"]},
          }

          return {
            P_GEN_TARGET: inputValues?.P_GEN_TARGET?.value,
            W_MIN: getValueEminEmax('W_MIN', inputValues, dataForStation.E_MAX_E_MIN),
            W_MAX: getValueEminEmax('W_MAX', inputValues, dataForStation.E_MAX_E_MIN),
            FLOOD_MODE_WATCH: prev['FLOOD_MODE_WATCH'],
          }
        })
        //INPUT VALUES
      }
    }
  }, [dataUp, hotUp]) //dataUp,inputValues.FLOOD_MODE_WATCH,hotUp

  const updateFloodWatch = (FLOOD_MODE_WATCH: boolean) => {
    // const FLOOD_MODE_WATCH = inputValues?.FLOOD_MODE_WATCH
    const REGULATED_UNIT = dataForStation.REGULATED_UNIT ?? 'PLANT'
    const RGU_MIN = dataForStation.rgus.map((_, index) => {
      return getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MIN_RESULT, index, dataForStation.rgus.length)
    })
    const RGU_PGEN = dataForStation.rgus.map((_, index) => {
      return getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_GEN, index, dataForStation.rgus.length)
    })
    const RGU_MAX = dataForStation.rgus.map((_, index) => {
      return getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MAX_RESULT, index, dataForStation.rgus.length)
    })
    setDataUp((prev) => {
      return prev.map((itemRow, index) => {
        return itemRow.map((itemCell, indexx) => {
          const isFindPMin = RGU_MIN.some((el) => el === indexx)
          const isFindPGen = RGU_PGEN.some((el) => el === indexx)
          const isFindPMax = RGU_MAX.some((el) => el === indexx)
          if (isFindPMin) {
            return destroyPMinFormula(itemCell)
          }
          if (isFindPGen && FLOOD_MODE_WATCH) {
            const rguPGenIdx = RGU_PGEN.findIndex((idx) => idx === indexx)
            const pMaxColumnIndex = getRguColumnIndex(
              'LIMITS',
              COLUMN_POSITIONS.P_MAX,
              rguPGenIdx,
              dataForStation.rgus.length,
            )

            return calculateSpreadsheetValueByFormula(
              hotUp,
              `=ROUND(MAX(0,${calcCellFromAlphabet(pMaxColumnIndex + 1)}${index + 1}),3)`,
            )
          }
          if (isFindPMax) {
            return destroyPMaxFormula(itemCell)
          }
          if (indexx === 0) {
            if (FLOOD_MODE_WATCH) {
              if (REGULATED_UNIT === 'PLANT') {
                const pMaxResultColumnIndex = getStationColumnIndex(
                  'RESULTS',
                  COLUMN_POSITIONS.P_MAX_RESULT,
                  dataForStation.rgus.length,
                )

                return `=ROUND(MAX(0,${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1}) - 1,3)`
              } else {
                const rgusSum = dataForStation.rgus.map((_, key) => {
                  const pMinResultColumnIndex = getRguColumnIndex(
                    'RESULTS',
                    COLUMN_POSITIONS.P_MIN_RESULT,
                    key,
                    dataForStation.rgus.length,
                  )

                  return `${calcCellFromAlphabet(pMinResultColumnIndex + 1)}${index + 1}`
                })
                const rgusForPminString = rgusSum.join('+')

                return `=ROUND(MAX(0,${rgusForPminString}),3)`
              }
            } else {
              const pMinColumnIndex = getStationColumnIndex(
                'LIMITS',
                COLUMN_POSITIONS.P_MIN,
                dataForStation.rgus.length,
              )
              const avrchmColumnIndex = getStationColumnIndex(
                'RESERVES',
                COLUMN_POSITIONS.AVRCHM_LOAD,
                dataForStation.rgus.length,
              )

              return `=ROUND(MAX(0,${calcCellFromAlphabet(pMinColumnIndex + 1)}${index + 1}+${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}),3)`
            }
          }
          if (indexx === 1 && FLOOD_MODE_WATCH) {
            if (REGULATED_UNIT === 'PLANT') {
              const pMaxColumnIndex = getStationColumnIndex(
                'LIMITS',
                COLUMN_POSITIONS.P_MAX,
                dataForStation.rgus.length,
              )

              return calculateSpreadsheetValueByFormula(
                hotUp,
                `=ROUND(MAX(0,${calcCellFromAlphabet(pMaxColumnIndex + 1)}${index + 1}),3)`,
              )
            } else {
              const rgusSum = dataForStation.rgus.map((_, key) => {
                const pMaxColumnIndex = getRguColumnIndex(
                  'LIMITS',
                  COLUMN_POSITIONS.P_MAX,
                  key,
                  dataForStation.rgus.length,
                )

                return `ROUND(MAX(0,${calcCellFromAlphabet(pMaxColumnIndex + 1)}${index + 1}),3)`
              })
              // сумма Итог.Огр (макс)
              const rgusForPGenString = rgusSum.join('+')

              return calculateSpreadsheetValueByFormula(hotUp, `=ROUND(MAX(0,${rgusForPGenString}),3)`)
            }
          }
          if (indexx === 2) {
            const pMaxColumnIndex = getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MAX, dataForStation.rgus.length)
            const avrchmColumnIndex = getStationColumnIndex(
              'RESERVES',
              COLUMN_POSITIONS.AVRCHM_LOAD,
              dataForStation.rgus.length,
            )
            if (FLOOD_MODE_WATCH) {
              if (REGULATED_UNIT === 'PLANT') {
                return `=ROUND(MAX(0,${calcCellFromAlphabet(pMaxColumnIndex + 1)}${index + 1}-${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}),3)`
              } else {
                const rgusSum = dataForStation.rgus.map((_, key) => {
                  const pMaxResultColumnIndex = getRguColumnIndex(
                    'RESULTS',
                    COLUMN_POSITIONS.P_MAX_RESULT,
                    key,
                    dataForStation.rgus.length,
                  )

                  return `${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1}`
                })
                const rgusForPminString = rgusSum.join('+')

                return `=ROUND(MAX(0,${rgusForPminString}),3)`
              }
            } else {
              return `=ROUND(MAX(0,${calcCellFromAlphabet(pMaxColumnIndex + 1)}${index + 1}-${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}),3)`
            }
          }

          return itemCell
        })
      })
    })
    const avrchmsKeys = []
    const avrchmPlant = getStationColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, dataForStation.rgus.length)
    avrchmsKeys.push(avrchmPlant)
    dataForStation.rgus.forEach((_, index) => {
      const rguAvrchmIndex = getRguColumnIndex(
        'RESERVES',
        COLUMN_POSITIONS.AVRCHM_LOAD,
        index,
        dataForStation.rgus.length,
      )
      avrchmsKeys.push(rguAvrchmIndex)
    })
    setCellUp((prev) => {
      return prev.map((el) => {
        const col = el.col
        let comment = el?.comment?.value ?? null
        const partToRemoveAvrchm = 'сумма АВРЧМ РГЕ ≠ АВРЧМ станции'
        if (comment !== null) {
          comment = comment.replace(partToRemoveAvrchm, '')
          comment = comment.trim()
        }
        const isAvrchmKey = avrchmsKeys.some((el) => el === col)
        const isAvrchmKeyPlant = col === avrchmsKeys[0]
        const isValid = true
        const renderer = isAvrchmKeyPlant
          ? getStatusCell(
              el.isMaxConsumptionHour,
              el.isMinConsumptionHour,
              FLOOD_MODE_WATCH ? false : 'numeric',
              el.keyStation,
              el.type,
              isValid,
              el?.manual || el?.fixed,
            )
          : el.renderer

        return {
          ...el,
          allowedZones: [],
          comment: comment === null || comment === '' ? null : getStyledComment(comment),
          renderer,
          editor: el.keyStation === 'RESERVES_MAX' ? false : isAvrchmKey ? FLOOD_MODE_WATCH : el.editor,
        }
      })
    })
    setSignalUpdateChart(true)
  }

  const updateDataStation = (FLOOD_MODE_WATCH: boolean, prop: any, rowEdit: any, newValue: any) => {
    // Индекс колонки AVRCHM_LOAD для станции или РГУ
    const indexAvrchm =
      dataForStation.REGULATED_UNIT === 'PLANT'
        ? getStationColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, dataForStation.rgus.length)
        : (dataForStation.rgus.length + 1) * COLUMN_BLOCK_SIZES.RESULTS
    // Индексы колонок AVRCHM_LOAD для всех РГУ + станция
    const avrchmIdxs =
      dataForStation.REGULATED_UNIT === 'PLANT'
        ? [indexAvrchm] // Для PLANT только индекс станции
        : dataForStation.rgus
            .map((_, idx) =>
              getRguColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, idx, dataForStation.rgus.length),
            )
            .concat([indexAvrchm]) // Для RGU индексы всех РГУ + станция
    if (avrchmIdxs.includes(Number(prop))) {
      calculationsPageStore.cleanAvrchmsByHourStation(rowEdit)
    }
    if (FLOOD_MODE_WATCH) {
      calculationsPageStore.cleanAvrchmsByHourStationAll()
      if (dataForStation.REGULATED_UNIT === 'PLANT') {
        setDataUp((rows: any) => {
          return rows.map((row: any, index: any) => {
            return row.map((el: any, indexEl: any) => {
              if (indexEl === 0) {
                // P_MIN_RESULT = P_MAX_RESULT - 1 в режиме половодья для станции
                const pMaxResultColumnIndex = getStationColumnIndex(
                  'RESULTS',
                  COLUMN_POSITIONS.P_MAX_RESULT,
                  dataForStation.rgus.length,
                )
                const P_MAX_RESULT = `${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1}`

                return `=ROUND(MAX(0,(${P_MAX_RESULT} - 1)),3)`
              }
              if (indexEl === prop && index === rowEdit) {
                return newValue
              }
              // Обнуляем AVRCHM_LOAD при включении режима половодья
              if (indexEl === indexAvrchm) {
                return 0
              }

              return el
            })
          })
        })
      }
      if (dataForStation.REGULATED_UNIT === 'RGU') {
        const countRgu = dataForStation.rgus.length
        const indexsRguAvrchm: any = dataForStation.rgus.map((_: any, index: number) => {
          return getRguColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, index, dataForStation.rgus.length)
        })
        setDataUp((rows: any) => {
          return rows.map((row: any, index: any) => {
            const sumMin: any = []
            const sumMax: any = []
            const indexsRgusMin = dataForStation?.rgus?.map((_: any, index: number) => {
              return getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MIN_RESULT, index, dataForStation.rgus.length)
            })
            dataForStation.rgus.forEach((_, indexx: number) => {
              const pMinResultColumnIndex = getRguColumnIndex(
                'RESULTS',
                COLUMN_POSITIONS.P_MIN_RESULT,
                indexx,
                dataForStation.rgus.length,
              )
              const pMaxResultColumnIndex = getRguColumnIndex(
                'RESULTS',
                COLUMN_POSITIONS.P_MAX_RESULT,
                indexx,
                dataForStation.rgus.length,
              )
              sumMin.push(`${calcCellFromAlphabet(pMinResultColumnIndex + 1)}${index + 1}`)
              sumMax.push(`${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1}`)
            })

            return row.map((el: any, indexEl: any) => {
              const isAvrchmRgu = indexsRguAvrchm.some((indexRgu: any) => indexRgu === indexEl)
              const isRguMin = indexsRgusMin.some((i: number) => i === indexEl)
              const zones =
                dataForStation?.allowedZones[index]?.zones.length > 0 ? dataForStation?.allowedZones[index]?.zones : []
              if (indexEl === 0) {
                const SUM_RGU_PMIN = `${sumMin.join('+')}`

                // return `=ROUND(MAX(0,${SUM_RGU_PMIN}),3)`
                return makePMinFormula(zones, `ROUND(MAX(0,${SUM_RGU_PMIN}),3)`)
              }
              if (indexEl === 2) {
                const SUM_RGU_PMAX = `${sumMax.join('+')}`

                // return `=ROUND(${SUM_RGU_PMAX},3)`
                return makePMaxFormula(zones, `ROUND(${SUM_RGU_PMAX},3)`)
              }
              if (indexEl === prop && index === rowEdit) {
                return newValue
              }
              if (indexEl === indexAvrchm + 1 || isAvrchmRgu) {
                return 0
              }
              if (isRguMin) {
                const indexx = indexsRgusMin.findIndex((el: any) => el === indexEl)

                return FLOOD_MODE_WATCH
                  ? (() => {
                      // Для режима FLOOD_MODE_WATCH используем P_MAX_RESULT из блока результатов
                      const pMaxResultColumnIndex = getRguColumnIndex(
                        'RESULTS',
                        COLUMN_POSITIONS.P_MAX_RESULT,
                        indexx,
                        dataForStation.rgus.length,
                      )

                      return `=ROUND(MAX(0,${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1} - 1),3)`
                    })()
                  : (() => {
                      // Для обычного режима используем P_MIN + AVRCHM_LOAD
                      const pMinColumnIndex = getRguColumnIndex(
                        'LIMITS',
                        COLUMN_POSITIONS.P_MIN,
                        indexx,
                        dataForStation.rgus.length,
                      )
                      const avrchmColumnIndex = getRguColumnIndex(
                        'RESERVES',
                        COLUMN_POSITIONS.AVRCHM_LOAD,
                        indexx,
                        dataForStation.rgus.length,
                      )

                      return `=ROUND(MAX(0,${calcCellFromAlphabet(pMinColumnIndex + 1)}${index + 1}+${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}),3)`
                    })()
              }

              return el
            })
          })
        })
      }
      setCellUp((prev) => {
        return prev.map((el) => {
          if (avrchmIdxs.includes(el.col)) {
            return { ...el, manual: undefined, fixed: undefined }
          }

          return el
        })
      })
    }
    if (!FLOOD_MODE_WATCH) {
      setDataUp((rows: any) => {
        return rows.map((row: any, index: any) => {
          return row.map((el: any, indexEl: any) => {
            const plantZones: IAllowedZone[] =
              !!dataForStation?.allowedZones && !!dataForStation?.allowedZones[index]?.zones
                ? dataForStation?.allowedZones[index]?.zones
                : []
            if (indexEl === 0) {
              let rguCells: any = []
              dataForStation.rgus
                .map((el: any) => ({
                  ...el.rows[index],
                  rguId: el.rguId,
                  allowedZones: el?.allowedZones || [],
                }))
                .forEach((el: any, indexx: number) => {
                  const cells = el.cells.map((cell: any) => ({
                    ...cell,
                    rguId: el.rguId,
                  }))
                  const P_GEN = cells.find((cell: any) => cell.column === 'P_GEN')?.value ?? '0'
                  const AVRCHM_LOAD = cells.find((cell: any) => cell.column === 'AVRCHM_LOAD')
                  const NPRCH = cells.find((cell: any) => cell.column === 'NPRCH')?.value ?? '0'
                  const CM_P_MIN = cells.find((cell: any) => cell.column === 'CM_P_MIN')
                  const CM_P_MAX = cells.find((cell: any) => cell.column === 'CM_P_MAX')
                  const MODES_P_MIN = cells.find((cell: any) => cell.column === 'MODES_P_MIN')
                  const MODES_P_MAX = cells.find((cell: any) => cell.column === 'MODES_P_MAX')
                  const MODES_DECLARED = cells.find((cell: any) => cell.column === 'MODES_DECLARED')
                  const avrchmLoad = AVRCHM_LOAD?.value ?? '0'

                  // LIMIT_MIN для РГУ: MAX(CM_P_MIN, MODES_P_MIN)
                  const getPmin = () => {
                    const cmPMinColumnIndex = getRguColumnIndex(
                      'CM',
                      COLUMN_POSITIONS.CM_P_MIN,
                      indexx,
                      dataForStation.rgus.length,
                    )
                    const modesPMinColumnIndex = getRguColumnIndex(
                      'MODES',
                      COLUMN_POSITIONS.MODES_P_MIN,
                      indexx,
                      dataForStation.rgus.length,
                    )

                    return `MAX(${calcCellFromAlphabet(cmPMinColumnIndex + 1)}${index + 1},${calcCellFromAlphabet(modesPMinColumnIndex + 1)}${index + 1})`
                  }
                  // LIMIT_MAX для РГУ: MIN(CM_P_MAX, MODES_P_MAX)
                  const getPmax = () => {
                    const cmPMaxColumnIndex = getRguColumnIndex(
                      'CM',
                      COLUMN_POSITIONS.CM_P_MAX,
                      indexx,
                      dataForStation.rgus.length,
                    )
                    const modesPMaxColumnIndex = getRguColumnIndex(
                      'MODES',
                      COLUMN_POSITIONS.MODES_P_MAX,
                      indexx,
                      dataForStation.rgus.length,
                    )

                    return `MIN(${calcCellFromAlphabet(cmPMaxColumnIndex + 1)}${index + 1},${calcCellFromAlphabet(modesPMaxColumnIndex + 1)}${index + 1})`
                  }
                  const P_MIN = getPmin()
                  const P_MAX = getPmax()
                  // P_MIN_RESULT для РГУ: P_MIN + AVRCHM_LOAD
                  const P_MIN_RESULT = (() => {
                    const pMinColumnIndex = getRguColumnIndex(
                      'LIMITS',
                      COLUMN_POSITIONS.P_MIN,
                      indexx,
                      dataForStation.rgus.length,
                    )
                    const avrchmColumnIndex = getRguColumnIndex(
                      'RESERVES',
                      COLUMN_POSITIONS.AVRCHM_LOAD,
                      indexx,
                      dataForStation.rgus.length,
                    )

                    return `${calcCellFromAlphabet(pMinColumnIndex + 1)}${index + 1}+${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}`
                  })()
                  // P_MAX_RESULT для РГУ: P_MAX - AVRCHM_LOAD
                  const P_MAX_RESULT = (() => {
                    const pMaxColumnIndex = getRguColumnIndex(
                      'LIMITS',
                      COLUMN_POSITIONS.P_MAX,
                      indexx,
                      dataForStation.rgus.length,
                    )
                    const avrchmColumnIndex = getRguColumnIndex(
                      'RESERVES',
                      COLUMN_POSITIONS.AVRCHM_LOAD,
                      indexx,
                      dataForStation.rgus.length,
                    )

                    return `${calcCellFromAlphabet(pMaxColumnIndex + 1)}${index + 1}-${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}`
                  })()
                  const RESERVES_MAX = cells.find((cell: any) => cell.column === 'RESERVES_MAX')?.value ?? undefined

                  const zones = !!el?.allowedZones && !!el?.allowedZones[index] ? el?.allowedZones[index]?.zones : []
                  rguCells = [
                    ...rguCells,
                    {
                      P_MIN_RESULT: makePMinFormula(zones, `ROUND(MAX(0,${P_MIN_RESULT}),3)`),
                      P_GEN: `${P_GEN}`,
                      P_MAX_RESULT: makePMaxFormula(zones, `ROUND(${P_MAX_RESULT},3)`),

                      RESERVES_MAX: RESERVES_MAX ? `=ROUND(${RESERVES_MAX},3)` : '',
                      AVRCHM: `${avrchmLoad}`,
                      NPRCH: `${NPRCH}`,

                      P_MIN: `=ROUND(${P_MIN},3)`,
                      P_MAX: `=ROUND(${P_MAX},3)`,

                      CM_P_MIN: CM_P_MIN ? CM_P_MIN?.value : '',
                      CM_P_MAX: CM_P_MAX ? CM_P_MAX?.value : '',

                      MODES_P_MIN: MODES_P_MIN ? MODES_P_MIN?.value : '',
                      MODES_P_MAX: MODES_P_MAX ? MODES_P_MAX?.value : '',
                      MODES_DECLARED: MODES_DECLARED ? MODES_DECLARED?.value : '',
                    },
                  ]
                })
              let RGU_P_GEN: any = []
              let RGU_RESERVE: any = []
              let RGU_OGR: any = []
              let RGU_RM: any = []
              let RGU_MODES: any = []

              const sumMin: any = []
              const sumMax: any = []

              rguCells.forEach((el: any, indexx: number) => {
                // Ссылки на P_MIN_RESULT и P_MAX_RESULT для каждой РГУ
                const pMinResultColumnIndex = getRguColumnIndex(
                  'RESULTS',
                  COLUMN_POSITIONS.P_MIN_RESULT,
                  indexx,
                  dataForStation.rgus.length,
                )
                const pMaxResultColumnIndex = getRguColumnIndex(
                  'RESULTS',
                  COLUMN_POSITIONS.P_MAX_RESULT,
                  indexx,
                  dataForStation.rgus.length,
                )

                sumMin.push(`${calcCellFromAlphabet(pMinResultColumnIndex + 1)}${index + 1}`)
                sumMax.push(`${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1}`)

                RGU_P_GEN = [
                  ...RGU_P_GEN,
                  `=ROUND(${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1} - 1,3)`, // P_MAX_RESULT - 1 для P_MIN_RESULT
                  el.P_GEN,
                  el.P_MAX_RESULT,
                ]
                RGU_RESERVE = [...RGU_RESERVE, el.RESERVES_MAX, el.AVRCHM, el.NPRCH]
                RGU_OGR = [...RGU_OGR, el.P_MIN, el.P_MAX]
                RGU_RM = [...RGU_RM, el.CM_P_MIN, el.CM_P_MAX]
                RGU_MODES = [...RGU_MODES, el.MODES_P_MIN, el.MODES_P_MAX, el.MODES_DECLARED]
              })
              // P_MIN_RESULT для станции: P_MIN + AVRCHM_LOAD
              const P_MIN_RESULT_PLANT = (() => {
                const pMinStationColumnIndex = getStationColumnIndex(
                  'LIMITS',
                  COLUMN_POSITIONS.P_MIN,
                  dataForStation.rgus.length,
                )
                const avrchmStationColumnIndex = getStationColumnIndex(
                  'RESERVES',
                  COLUMN_POSITIONS.AVRCHM_LOAD,
                  dataForStation.rgus.length,
                )

                return `${calcCellFromAlphabet(pMinStationColumnIndex + 1)}${index + 1}+${calcCellFromAlphabet(avrchmStationColumnIndex + 1)}${index + 1}`
              })()

              return makePMinFormula(plantZones, `ROUND(MAX(0,${P_MIN_RESULT_PLANT}),3)`)
            }
            if (indexEl === 2) {
              // P_MAX_RESULT для станции: P_MAX - AVRCHM_LOAD
              const P_MAX_RESULT = (() => {
                const pMaxStationColumnIndex = getStationColumnIndex(
                  'LIMITS',
                  COLUMN_POSITIONS.P_MAX,
                  dataForStation.rgus.length,
                )
                const avrchmStationColumnIndex = getStationColumnIndex(
                  'RESERVES',
                  COLUMN_POSITIONS.AVRCHM_LOAD,
                  dataForStation.rgus.length,
                )

                return `${calcCellFromAlphabet(pMaxStationColumnIndex + 1)}${index + 1}-${calcCellFromAlphabet(avrchmStationColumnIndex + 1)}${index + 1}`
              })()

              return makePMaxFormula(plantZones, `ROUND(${P_MAX_RESULT},3)`)
            }
            if (indexEl === prop && index === rowEdit) {
              return newValue
            }
            const sumMin: any = []
            const sumMax: any = []
            const indexsRgusMin = dataForStation?.rgus?.map((_: any, index: number) => {
              return getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MIN_RESULT, index, dataForStation.rgus.length)
            })
            const indexsRgusMax = dataForStation?.rgus?.map((_: any, index: number) => {
              return getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MAX_RESULT, index, dataForStation.rgus.length)
            })
            dataForStation.rgus.forEach((_, indexx: number) => {
              const pMinResultColumnIndex = getRguColumnIndex(
                'RESULTS',
                COLUMN_POSITIONS.P_MIN_RESULT,
                indexx,
                dataForStation.rgus.length,
              )
              const pMaxResultColumnIndex = getRguColumnIndex(
                'RESULTS',
                COLUMN_POSITIONS.P_MAX_RESULT,
                indexx,
                dataForStation.rgus.length,
              )
              sumMin.push(`${calcCellFromAlphabet(pMinResultColumnIndex + 1)}${index + 1}`)
              sumMax.push(`${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1}`)
            })
            const isRguMin = indexsRgusMin.some((i: number) => i === indexEl)
            if (isRguMin) {
              const indexx = indexsRgusMin.findIndex((el: any) => el === indexEl)
              const zones =
                dataForStation?.rgus[indexx] && dataForStation?.rgus[indexx]?.allowedZones?.length > 0
                  ? dataForStation?.rgus[indexx]?.allowedZones[index]?.zones
                  : []

              return makePMinFormula(
                zones,
                FLOOD_MODE_WATCH
                  ? (() => {
                      const pMaxResultColumnIndex = getRguColumnIndex(
                        'RESULTS',
                        COLUMN_POSITIONS.P_MAX_RESULT,
                        indexx,
                        dataForStation.rgus.length,
                      )

                      return `ROUND(MAX(0,${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1} - 1),3)`
                    })()
                  : (() => {
                      const pMinColumnIndex = getRguColumnIndex(
                        'LIMITS',
                        COLUMN_POSITIONS.P_MIN,
                        indexx,
                        dataForStation.rgus.length,
                      )
                      const avrchmColumnIndex = getRguColumnIndex(
                        'RESERVES',
                        COLUMN_POSITIONS.AVRCHM_LOAD,
                        indexx,
                        dataForStation.rgus.length,
                      )

                      return `ROUND(MAX(0,${calcCellFromAlphabet(pMinColumnIndex + 1)}${index + 1}+${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}),3)`
                    })(),
              )
            }
            const isRguMax = indexsRgusMax.some((i: number) => i === indexEl)
            if (isRguMax) {
              const indexx = indexsRgusMax.findIndex((el: any) => el === indexEl)
              const zones =
                dataForStation?.rgus[indexx] && dataForStation?.rgus[indexx]?.allowedZones?.length > 0
                  ? dataForStation?.rgus[indexx]?.allowedZones[index]?.zones
                  : []

              return makePMaxFormula(
                zones,
                FLOOD_MODE_WATCH
                  ? (() => {
                      const pMaxResultColumnIndex = getRguColumnIndex(
                        'RESULTS',
                        COLUMN_POSITIONS.P_MAX_RESULT,
                        indexx,
                        dataForStation.rgus.length,
                      )

                      return `ROUND(MAX(0,${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1} - 1),3)`
                    })()
                  : (() => {
                      const pMaxColumnIndex = getRguColumnIndex(
                        'LIMITS',
                        COLUMN_POSITIONS.P_MAX,
                        indexx,
                        dataForStation.rgus.length,
                      )
                      const avrchmColumnIndex = getRguColumnIndex(
                        'RESERVES',
                        COLUMN_POSITIONS.AVRCHM_LOAD,
                        indexx,
                        dataForStation.rgus.length,
                      )

                      return `ROUND(MAX(0,${calcCellFromAlphabet(pMaxColumnIndex + 1)}${index + 1}-${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}),3)`
                    })(),
              )
            }

            return el
          })
        })
      })
      setCellUp((prev) => {
        return prev.map((el) => {
          if (avrchmIdxs.includes(el.col) && el.keyStation !== 'RESERVES_MAX') {
            return { ...el, editor: 'numeric' }
          }

          return el
        })
      })
    }
    const disabled = dataForStation.accepted || viewOnly || isLastDay || isFinishStage || !editMode
    let columnFirst: any = disabled
      ? [
          { editor: false, readOnly: false },
          { editor: false, readOnly: false },
          { editor: false, readOnly: false },
        ]
      : [
          { editor: false, readOnly: false },
          { editor: 'numeric', readOnly: false },
          { editor: false, readOnly: false },
        ]
    dataForStation.rgus.forEach(() => {
      columnFirst = disabled
        ? [
            ...columnFirst,
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
          ]
        : [
            ...columnFirst,
            { editor: false, readOnly: false },
            { editor: 'numeric', readOnly: false },
            { editor: false, readOnly: false },
          ]
    })
    const columnSecondTmp: any = disabled
      ? [
          { editor: false, readOnly: false },
          { editor: false, readOnly: false },
          { editor: false, readOnly: false },
        ]
      : [
          { editor: false, readOnly: false },
          FLOOD_MODE_WATCH ? { editor: false, readOnly: false } : { editor: 'numeric' },
          { editor: 'numeric' },
        ]
    let columnSecond = columnSecondTmp
    dataForStation.rgus.forEach(() => {
      columnSecond = [...columnSecond, ...columnSecondTmp]
    })
    let columnThird: any = disabled
      ? [
          { editor: false, readOnly: false },
          { editor: false, readOnly: false },
        ]
      : [{ editor: 'numeric' }, { editor: 'numeric' }]
    dataForStation.rgus.forEach(() => {
      columnThird = disabled
        ? [...columnThird, { editor: false, readOnly: false }, { editor: false, readOnly: false }]
        : [...columnThird, { editor: 'numeric' }, { editor: 'numeric' }]
    })
    const columnTemp: any = [
      ...columnFirst, // Итог (мин, план, макс)
      ...columnSecond, // Резервы (Rмакс, АВРЧМ)
      ...columnThird.map(() => ({ editor: false, readOnly: false })), // Итог.ОГР (мин, макс)
      ...columnThird, // РМ (мин, макс)
      ...columnFirst.map(() => ({ editor: false, readOnly: false })), // Модес (мин, макс, заяв)
      { editor: false, readOnly: false }, // ИСП (потр)
    ]
    setColumnUp(columnTemp)
  }

  const getIspDate = (value: string): any => {
    const [year, month, day] = value.split('-')

    return new Date(`${year}-${month}-${day}`)
  }

  const prepareDataStation = (props: ICalculationsPageStore['dataForStation']) => {
    if (props) {
      const {
        inputValues,
        rows,
        allowedZones,
        rgus,
        accepted,
        maxConsumptionHour,
        minConsumptionHour,
        plantOptimized,
        E_MAX_E_MIN,
        REGULATED_UNIT,
        ISP_DATE,
        columnStages,
      } = props
      const { year = null, month = null, day = null } = locationParse(location.search)
      const nowDate = new Date()
      nowDate.setDate(nowDate.getDate() + 1)
      const dateISPInit = ISP_DATE
        ? getIspDate(ISP_DATE)
        : year && month && day
          ? new Date(`${year}-${month}-${day}`)
          : nowDate
      setDateISP(dateISPInit)
      const disabled = accepted || viewOnly || isLastDay || isFinishStage || !editMode
      const FLOOD_MODE_WATCH = inputValues?.FLOOD_MODE_WATCH?.value?.turnedOn ?? false
      const dataTemp = new Array(24).fill([]).map((_, index: number) => {
        const plantZones = allowedZones[index]?.zones?.length > 0 ? allowedZones[index]?.zones : []
        const hourData = rows[index]?.cells ?? []
        const P_GEN = hourData?.find((el) => el.column === 'P_GEN')
        const AVRCHM_LOAD = hourData?.find((el) => el.column === 'AVRCHM_LOAD')?.value ?? '0'
        const NPRCH = hourData?.find((el) => el.column === 'NPRCH')
        const CM_P_MIN = hourData?.find((el) => el.column === 'CM_P_MIN')
        const CM_P_MAX = hourData?.find((el) => el.column === 'CM_P_MAX')
        const MODES_P_MIN = hourData?.find((el) => el.column === 'MODES_P_MIN')
        const MODES_P_MAX = hourData?.find((el) => el.column === 'MODES_P_MAX')
        const MODES_DECLARED = hourData?.find((el) => el.column === 'MODES_DECLARED')
        const CONSUMPT = hourData?.find((el) => el.column === 'CONSUMPT')

        let rguCells: IRguCells = []
        rgus
          .map((el) => ({
            ...el.rows[index],
            rguId: el.rguId,
            allowedZones: el.allowedZones,
          }))
          .forEach((el, indexx) => {
            const cells = el.cells.map((cell) => ({
              ...cell,
              rguId: el.rguId,
            }))
            const P_GEN = cells.find((cell) => cell.column === 'P_GEN')
            const AVRCHM_LOAD = cells.find((cell) => cell.column === 'AVRCHM_LOAD')
            const NPRCH = cells.find((cell) => cell.column === 'NPRCH')
            const nprch = NPRCH?.value ?? '0'
            const CM_P_MIN = cells.find((cell) => cell.column === 'CM_P_MIN')
            const CM_P_MAX = cells.find((cell) => cell.column === 'CM_P_MAX')
            const MODES_P_MIN = cells.find((cell) => cell.column === 'MODES_P_MIN')
            const MODES_P_MAX = cells.find((cell) => cell.column === 'MODES_P_MAX')
            const MODES_DECLARED = cells.find((cell) => cell.column === 'MODES_DECLARED')
            const avrchmLoad = AVRCHM_LOAD?.value ?? '0'

            // LIMIT_MIN для РГУ
            const getPmin = () => {
              // MAX(CM_P_MIN, MODES_P_MIN) для РГУ
              const cmPMinColumnIndex = getRguColumnIndex('CM', COLUMN_POSITIONS.CM_P_MIN, indexx, rgus.length)
              const modesPMinColumnIndex = getRguColumnIndex('MODES', COLUMN_POSITIONS.MODES_P_MIN, indexx, rgus.length)

              return `MAX(${calcCellFromAlphabet(cmPMinColumnIndex + 1)}${index + 1},${calcCellFromAlphabet(modesPMinColumnIndex + 1)}${index + 1})`
            }
            // LIMIT_MAX для РГУ
            const getPmax = () => {
              // MIN(CM_P_MAX, MODES_P_MAX) для РГУ
              const cmPMaxColumnIndex = getRguColumnIndex('CM', COLUMN_POSITIONS.CM_P_MAX, indexx, rgus.length)
              const modesPMaxColumnIndex = getRguColumnIndex('MODES', COLUMN_POSITIONS.MODES_P_MAX, indexx, rgus.length)

              return `MIN(${calcCellFromAlphabet(cmPMaxColumnIndex + 1)}${index + 1},${calcCellFromAlphabet(modesPMaxColumnIndex + 1)}${index + 1})`
            }
            const P_MIN = getPmin()
            const P_MAX = getPmax()
            const P_MIN_RESULT = FLOOD_MODE_WATCH
              ? (() => {
                  // Для режима FLOOD_MODE_WATCH: P_MAX_RESULT - 1
                  const pMaxResultColumnIndex = getRguColumnIndex(
                    'RESULTS',
                    COLUMN_POSITIONS.P_MAX_RESULT,
                    indexx,
                    rgus.length,
                  )

                  return `${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1} - 1`
                })()
              : (() => {
                  // Для обычного режима: P_MIN + AVRCHM_LOAD
                  const pMinColumnIndex = getRguColumnIndex('LIMITS', COLUMN_POSITIONS.P_MIN, indexx, rgus.length)
                  const avrchmColumnIndex = getRguColumnIndex(
                    'RESERVES',
                    COLUMN_POSITIONS.AVRCHM_LOAD,
                    indexx,
                    rgus.length,
                  )

                  return `${calcCellFromAlphabet(pMinColumnIndex + 1)}${index + 1}+${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}`
                })()
            const P_MAX_RESULT = (() => {
              // P_MAX - AVRCHM_LOAD
              const pMaxColumnIndex = getRguColumnIndex('LIMITS', COLUMN_POSITIONS.P_MAX, indexx, rgus.length)
              const avrchmColumnIndex = getRguColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, indexx, rgus.length)

              return `${calcCellFromAlphabet(pMaxColumnIndex + 1)}${index + 1}-${calcCellFromAlphabet(avrchmColumnIndex + 1)}${index + 1}`
            })()
            const RESERVES_MAX =
              cells.find((cell) => cell.column === CalculationColumn.RESERVES_MAX)?.value ?? undefined
            const zones = el?.allowedZones[index]?.zones?.length > 0 ? el?.allowedZones[index]?.zones : []
            const getPMINResultRgu = () => {
              return makePMinFormula(zones, `ROUND(MAX(0,${P_MIN_RESULT}),3)`)
            }
            const getPMAXResultRgu = () => {
              return makePMaxFormula(zones, `ROUND(${P_MAX_RESULT},3)`)
            }
            rguCells = [
              ...rguCells,
              {
                P_MIN_RESULT: getPMINResultRgu(),
                P_GEN: P_GEN ? P_GEN.value : '',
                P_MAX_RESULT: getPMAXResultRgu(),

                RESERVES_MAX: RESERVES_MAX !== undefined ? `=ROUND(${RESERVES_MAX},3)` : '',
                AVRCHM: FLOOD_MODE_WATCH ? 0 : `${avrchmLoad}`,
                NPRCH: `${nprch}`,

                P_MIN: `=ROUND(${P_MIN},3)`,
                P_MAX: `=ROUND(${P_MAX},3)`,

                CM_P_MIN: CM_P_MIN ? CM_P_MIN.value : '',
                CM_P_MAX: CM_P_MAX ? CM_P_MAX.value : '',

                MODES_P_MIN: MODES_P_MIN ? MODES_P_MIN.value : '',
                MODES_P_MAX: MODES_P_MAX ? MODES_P_MAX.value : '',
                MODES_DECLARED: MODES_DECLARED ? MODES_DECLARED.value : '',
              },
            ]
          })

        let RGU_P_GEN: (string | number)[] = []
        let RGU_RESERVE: (string | number)[] = []
        let RGU_OGR: string[] = []
        let RGU_RM: (string | number)[] = []
        let RGU_MODES: (string | number)[] = []
        rguCells.forEach((el) => {
          RGU_P_GEN = [...RGU_P_GEN, el.P_MIN_RESULT, el.P_GEN, el.P_MAX_RESULT]
          RGU_RESERVE = [...RGU_RESERVE, el.RESERVES_MAX, el.AVRCHM, el.NPRCH]
          RGU_OGR = [...RGU_OGR, el.P_MIN, el.P_MAX]
          RGU_RM = [...RGU_RM, el.CM_P_MIN, el.CM_P_MAX]
          RGU_MODES = [...RGU_MODES, el.MODES_P_MIN, el.MODES_P_MAX, el.MODES_DECLARED]
        })

        // Формулы для станции (не РГУ)
        const P_MIN_RESULT = (() => {
          // P_MIN + AVRCHM_LOAD для станции
          const pMinStationColumnIndex = getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MIN, rgus.length)
          const avrchmStationColumnIndex = getStationColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, rgus.length)

          return `${calcCellFromAlphabet(pMinStationColumnIndex + 1)}${index + 1}+${calcCellFromAlphabet(avrchmStationColumnIndex + 1)}${index + 1}`
        })()
        const P_MAX_RESULT = (() => {
          // P_MAX - AVRCHM_LOAD для станции
          const pMaxStationColumnIndex = getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MAX, rgus.length)
          const avrchmStationColumnIndex = getStationColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, rgus.length)

          return `${calcCellFromAlphabet(pMaxStationColumnIndex + 1)}${index + 1}-${calcCellFromAlphabet(avrchmStationColumnIndex + 1)}${index + 1}`
        })()

        const RESERVES_MAX = hourData?.find((el) => el.column === CalculationColumn.RESERVES_MAX)?.value ?? undefined

        // LIMIT_MIN для PLANT
        const getPmin = () => {
          // MAX(CM_P_MIN, MODES_P_MIN) для станции
          const cmPMinStationColumnIndex = getStationColumnIndex('CM', COLUMN_POSITIONS.CM_P_MIN, rgus.length)
          const modesPMinStationColumnIndex = getStationColumnIndex('MODES', COLUMN_POSITIONS.MODES_P_MIN, rgus.length)

          return `MAX(${calcCellFromAlphabet(cmPMinStationColumnIndex + 1)}${index + 1},${calcCellFromAlphabet(modesPMinStationColumnIndex + 1)}${index + 1})`
        }
        // LIMIT_MAX для PLANT
        const getPmax = () => {
          // MIN(CM_P_MAX, MODES_P_MAX) для станции
          const cmPMaxStationColumnIndex = getStationColumnIndex('CM', COLUMN_POSITIONS.CM_P_MAX, rgus.length)
          const modesPMaxStationColumnIndex = getStationColumnIndex('MODES', COLUMN_POSITIONS.MODES_P_MAX, rgus.length)

          return `MIN(${calcCellFromAlphabet(cmPMaxStationColumnIndex + 1)}${index + 1},${calcCellFromAlphabet(modesPMaxStationColumnIndex + 1)}${index + 1})`
        }
        const P_MIN = getPmin()
        const P_MAX = getPmax()
        // Формулы для суммирования P_MIN_RESULT и P_MAX_RESULT всех РГУ
        const sumMin: string[] = []
        const sumMax: string[] = []
        rguCells.forEach((_, indexx) => {
          // P_MIN_RESULT для каждой РГУ
          const pMinResultColumnIndex = getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MIN_RESULT, indexx, rgus.length)
          sumMin.push(`${calcCellFromAlphabet(pMinResultColumnIndex + 1)}${index + 1}`)

          // P_MAX_RESULT для каждой РГУ
          const pMaxResultColumnIndex = getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MAX_RESULT, indexx, rgus.length)
          sumMax.push(`${calcCellFromAlphabet(pMaxResultColumnIndex + 1)}${index + 1}`)
        })
        const SUM_RGU_PMIN = `${sumMin.join('+')}`
        const SUM_RGU_PMAX = `${sumMax.join('+')}`

        // P_MAX_RESULT станции для режима FLOOD_MODE_WATCH
        const key_P_MAX_RESULT = (() => {
          const pMaxResultStationColumnIndex = getStationColumnIndex(
            'RESULTS',
            COLUMN_POSITIONS.P_MAX_RESULT,
            rgus.length,
          )

          return `${calcCellFromAlphabet(pMaxResultStationColumnIndex + 1)}${index + 1}`
        })()

        const getPminRes = () => {
          if (FLOOD_MODE_WATCH) {
            if (REGULATED_UNIT === 'PLANT') {
              return makePMinFormula(plantZones, `ROUND(MAX(0,(${key_P_MAX_RESULT} - 1)),3)`)
            } else {
              return makePMinFormula(plantZones, `ROUND(MAX(0,${SUM_RGU_PMIN}),3)`)
            }
          } else {
            return makePMinFormula(plantZones, `ROUND(MAX(0,${P_MIN_RESULT}),3)`)
          }
        }
        const getPmaxRes = () => {
          if (FLOOD_MODE_WATCH) {
            if (REGULATED_UNIT === 'PLANT') {
              // В режиме половодья для станции: MAX(0, P_MAX - AVRCHM_LOAD)
              const pMaxStationColumnIndex = getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MAX, rgus.length)
              const avrchmStationColumnIndex = getStationColumnIndex(
                'RESERVES',
                COLUMN_POSITIONS.AVRCHM_LOAD,
                rgus.length,
              )
              const floodFormula = `MAX(0,${calcCellFromAlphabet(pMaxStationColumnIndex + 1)}${index + 1}-${calcCellFromAlphabet(avrchmStationColumnIndex + 1)}${index + 1})`

              return makePMaxFormula(plantZones, `ROUND(${floodFormula},3)`)
            } else {
              return makePMaxFormula(plantZones, `ROUND(${SUM_RGU_PMAX},3)`)
            }
          } else {
            return makePMaxFormula(plantZones, `ROUND(${P_MAX_RESULT},3)`)
          }
        }

        const pMinRes = getPminRes()
        const pMaxRes =
          REGULATED_UNIT === 'PLANT'
            ? getPmaxRes()
            : FLOOD_MODE_WATCH
              ? makePMaxFormula(plantZones, `ROUND(${SUM_RGU_PMAX},3)`)
              : getPmaxRes()

        // В режиме половодья для станции P_GEN должно быть равно P_MAX_RESULT
        const pGenValue =
          FLOOD_MODE_WATCH && REGULATED_UNIT === 'PLANT'
            ? pMaxRes // В режиме половодья P_GEN = P_MAX_RESULT
            : P_GEN
              ? `${P_GEN.value}`
              : ''

        return [
          pMinRes,
          pGenValue,
          pMaxRes,

          ...RGU_P_GEN,
          RESERVES_MAX !== undefined ? `=ROUND(${RESERVES_MAX},3)` : '',
          FLOOD_MODE_WATCH ? 0 : `${AVRCHM_LOAD}`,
          NPRCH ? `${NPRCH.value}` : '',

          ...RGU_RESERVE,

          `=ROUND(${P_MIN},3)`,
          `=ROUND(${P_MAX},3)`,

          ...RGU_OGR,

          CM_P_MIN ? `${CM_P_MIN?.value}` : '',
          CM_P_MAX ? `${CM_P_MAX?.value}` : '',

          ...RGU_RM,

          MODES_P_MIN ? MODES_P_MIN?.value : '',
          MODES_P_MAX ? MODES_P_MAX?.value : '',
          MODES_DECLARED ? MODES_DECLARED?.value : '',

          ...RGU_MODES,

          CONSUMPT ? `=ROUND(${CONSUMPT?.value},0)` : '',
        ]
      })
      setDataUp(dataTemp)
      let nestedFirst: IVaultNestedHeaders[0] = []
      rgus.forEach(() => {
        nestedFirst = [
          ...nestedFirst,
          { label: 'мин', colspan: 1, accepted },
          { label: 'план', colspan: 1, accepted },
          { label: 'макс', colspan: 1, accepted },
        ]
      })
      let nestedSecond: IVaultNestedHeaders[0] = []
      rgus.forEach(() => {
        nestedSecond = [
          ...nestedSecond,
          { label: 'Rмакс', colspan: 1, accepted },
          { label: 'АВРЧМ', colspan: 1, accepted },
          { label: 'НПРЧ', colspan: 1, accepted },
        ]
      })
      let nestedThird: IVaultNestedHeaders[0] = []
      rgus.forEach(() => {
        nestedThird = [...nestedThird, { label: 'мин', colspan: 1, accepted }, { label: 'макс', colspan: 1, accepted }]
      })
      let nestedFourth: IVaultNestedHeaders[0] = []
      rgus.forEach(() => {
        nestedFourth = [
          ...nestedFourth,
          { label: 'мин', colspan: 1, accepted },
          { label: 'макс', colspan: 1, accepted },
        ]
      })
      let nestedFifth: IVaultNestedHeaders[0] = []
      rgus.forEach(() => {
        nestedFifth = [
          ...nestedFifth,
          { label: 'мин', colspan: 1, accepted },
          { label: 'макс', colspan: 1, accepted },
          { label: 'заяв', colspan: 1, accepted },
        ]
      })
      let rguStageFirstBlock = []
      let rguStageSecondBlock = []
      let rguStageThirdBlock = []
      let rguStageFourthBlock = []
      let rguStageFifthBlock = []
      rgus.forEach((el) => {
        rguStageFirstBlock = [
          ...rguStageFirstBlock,
          {
            label: PlanningStageRussia[el.columnStages['RESULT_MIN']],
            colspan: 1,
            accepted,
            code: el.columnStages['RESULT_MIN'],
          },
          {
            label: PlanningStageRussia[el.columnStages['P_GEN']],
            colspan: 1,
            accepted,
            code: el.columnStages['P_GEN'],
          },
          {
            label: PlanningStageRussia[el.columnStages['RESULT_MAX']],
            colspan: 1,
            accepted,
            code: el.columnStages['RESULT_MAX'],
          },
        ]
        rguStageSecondBlock = [
          ...rguStageSecondBlock,
          {
            label: PlanningStageRussia[el.columnStages['RESERVES_MAX']],
            colspan: 1,
            accepted,
            code: el.columnStages['RESERVES_MAX'],
          },
          {
            label: PlanningStageRussia[el.columnStages['AVRCHM_LOAD']],
            colspan: 1,
            accepted,
            code: el.columnStages['AVRCHM_LOAD'],
          },
          {
            label: PlanningStageRussia[el.columnStages['NPRCH']],
            colspan: 1,
            accepted,
            code: el.columnStages['NPRCH'],
          },
        ]
        rguStageThirdBlock = [
          ...rguStageThirdBlock,
          {
            label: PlanningStageRussia[el.columnStages['LIMIT_MIN']],
            colspan: 1,
            accepted,
            code: el.columnStages['LIMIT_MIN'],
          },
          {
            label: PlanningStageRussia[el.columnStages['LIMIT_MAX']],
            colspan: 1,
            accepted,
            code: el.columnStages['LIMIT_MAX'],
          },
        ]
        rguStageFourthBlock = [
          ...rguStageFourthBlock,
          {
            label: PlanningStageRussia[el.columnStages['CM_P_MIN']],
            colspan: 1,
            accepted,
            code: el.columnStages['CM_P_MIN'],
          },
          {
            label: PlanningStageRussia[el.columnStages['CM_P_MAX']],
            colspan: 1,
            accepted,
            code: el.columnStages['CM_P_MAX'],
          },
        ]
        rguStageFifthBlock = [
          ...rguStageFifthBlock,
          {
            label: PlanningStageRussia[el.columnStages['MODES_P_MIN']],
            colspan: 1,
            accepted,
            code: el.columnStages['MODES_P_MIN'],
          },
          {
            label: PlanningStageRussia[el.columnStages['MODES_P_MAX']],
            colspan: 1,
            accepted,
            code: el.columnStages['MODES_P_MAX'],
          },
          {
            label: PlanningStageRussia[el.columnStages['MODES_DECLARED']],
            colspan: 1,
            accepted,
            code: el.columnStages['MODES_DECLARED'],
          },
        ]
      })
      const tempNestedHeadersUp: IVaultNestedHeaders =
        rgus.length > 0
          ? [
              [
                {
                  label: 'Итог',
                  colspan: COLUMN_BLOCK_SIZES.RESULTS * (rgus.length + 1),
                },
                {
                  label: 'Резервы',
                  colspan: COLUMN_BLOCK_SIZES.RESERVES * (rgus.length + 1),
                },
                {
                  label: 'Итог.ОГР',
                  colspan: COLUMN_BLOCK_SIZES.LIMITS * (rgus.length + 1),
                },
                { label: 'РМ', colspan: COLUMN_BLOCK_SIZES.CM * (rgus.length + 1) },
                {
                  label: 'Модес',
                  colspan: COLUMN_BLOCK_SIZES.MODES * (rgus.length + 1),
                },
                { label: 'ИСП', colspan: 1 },
              ],
              [
                { label: 'Σ', colspan: COLUMN_BLOCK_SIZES.RESULTS },
                ...rgus.map((el) => ({
                  label: el.rguName,
                  colspan: COLUMN_BLOCK_SIZES.RESULTS,
                })),
                { label: 'Σ', colspan: COLUMN_BLOCK_SIZES.RESERVES },
                ...rgus.map((el) => ({
                  label: el.rguName,
                  colspan: COLUMN_BLOCK_SIZES.RESERVES,
                })),
                { label: 'Σ', colspan: COLUMN_BLOCK_SIZES.LIMITS },
                ...rgus.map((el) => ({
                  label: el.rguName,
                  colspan: COLUMN_BLOCK_SIZES.LIMITS,
                })),
                { label: 'Σ', colspan: COLUMN_BLOCK_SIZES.CM },
                ...rgus.map((el) => ({
                  label: el.rguName,
                  colspan: COLUMN_BLOCK_SIZES.CM,
                })),
                { label: 'Σ', colspan: COLUMN_BLOCK_SIZES.MODES },
                ...rgus.map((el) => ({
                  label: el.rguName,
                  colspan: COLUMN_BLOCK_SIZES.MODES,
                })),
              ],
              [
                { label: 'мин', colspan: 1, accepted },
                { label: 'план', colspan: 1, accepted },
                { label: 'макс', colspan: 1, accepted },
                ...nestedFirst.map((el) => el),
                { label: 'Rмакс', colspan: 1, accepted },
                { label: 'АВРЧМ', colspan: 1, accepted },
                { label: 'НПРЧ', colspan: 1, accepted },
                ...nestedSecond.map((el) => el),
                { label: 'мин', colspan: 1, accepted },
                { label: 'макс', colspan: 1, accepted },
                ...nestedThird.map((el) => el),
                { label: 'мин', colspan: 1, accepted },
                { label: 'макс', colspan: 1, accepted },
                ...nestedFourth.map((el) => el),
                { label: 'мин', colspan: 1, accepted },
                { label: 'макс', colspan: 1, accepted },
                { label: 'заяв', colspan: 1, accepted },
                ...nestedFifth.map((el) => el),
                { label: 'потр', colspan: 1, accepted },
              ],
              [
                {
                  label: PlanningStageRussia[columnStages['RESULT_MIN']],
                  colspan: 1,
                  accepted,
                  code: columnStages['RESULT_MIN'],
                },
                {
                  label: PlanningStageRussia[columnStages['P_GEN']],
                  colspan: 1,
                  accepted,
                  code: columnStages['P_GEN'],
                },
                {
                  label: PlanningStageRussia[columnStages['RESULT_MAX']],
                  colspan: 1,
                  accepted,
                  code: columnStages['RESULT_MAX'],
                },
                ...rguStageFirstBlock,
                {
                  label: PlanningStageRussia[columnStages['RESERVES_MAX']],
                  colspan: 1,
                  accepted,
                  code: columnStages['RESERVES_MAX'],
                },
                {
                  label: PlanningStageRussia[columnStages['AVRCHM_LOAD']],
                  colspan: 1,
                  accepted,
                  code: columnStages['AVRCHM_LOAD'],
                },
                {
                  label: PlanningStageRussia[columnStages['NPRCH']],
                  colspan: 1,
                  accepted,
                  code: columnStages['NPRCH'],
                },
                ...rguStageSecondBlock,
                {
                  label: PlanningStageRussia[columnStages['LIMIT_MIN']],
                  colspan: 1,
                  accepted,
                  code: columnStages['LIMIT_MIN'],
                },
                {
                  label: PlanningStageRussia[columnStages['LIMIT_MAX']],
                  colspan: 1,
                  accepted,
                  code: columnStages['LIMIT_MAX'],
                },
                ...rguStageThirdBlock,
                {
                  label: PlanningStageRussia[columnStages['CM_P_MIN']],
                  colspan: 1,
                  accepted,
                  code: columnStages['CM_P_MIN'],
                },
                {
                  label: PlanningStageRussia[columnStages['CM_P_MAX']],
                  colspan: 1,
                  accepted,
                  code: columnStages['CM_P_MAX'],
                },
                ...rguStageFourthBlock,
                {
                  label: PlanningStageRussia[columnStages['MODES_P_MIN']],
                  colspan: 1,
                  accepted,
                  code: columnStages['MODES_P_MIN'],
                },
                {
                  label: PlanningStageRussia[columnStages['MODES_P_MAX']],
                  colspan: 1,
                  accepted,
                  code: columnStages['MODES_P_MAX'],
                },
                {
                  label: PlanningStageRussia[columnStages['MODES_DECLARED']],
                  colspan: 1,
                  accepted,
                  code: columnStages['MODES_DECLARED'],
                },
                ...rguStageFifthBlock,
                {
                  label: PlanningStageRussia[columnStages['CONSUMPT']],
                  colspan: 1,
                  accepted,
                  code: columnStages['CONSUMPT'],
                },
              ],
            ]
          : [
              [
                {
                  label: 'Итог',
                  colspan: COLUMN_BLOCK_SIZES.RESULTS * (rgus.length + 1),
                },
                {
                  label: 'Резервы',
                  colspan: COLUMN_BLOCK_SIZES.RESERVES * (rgus.length + 1),
                },
                {
                  label: 'Итог.ОГР',
                  colspan: COLUMN_BLOCK_SIZES.LIMITS * (rgus.length + 1),
                },
                { label: 'РМ', colspan: COLUMN_BLOCK_SIZES.CM * (rgus.length + 1) },
                {
                  label: 'Модес',
                  colspan: COLUMN_BLOCK_SIZES.MODES * (rgus.length + 1),
                },
                { label: 'ИСП', colspan: 1 },
              ],
              [
                { label: 'мин', colspan: 1, accepted },
                { label: 'план', colspan: 1, accepted },
                { label: 'макс', colspan: 1, accepted },
                ...nestedFirst.map((el) => el),
                { label: 'Rмакс', colspan: 1, accepted },
                { label: 'АВРЧМ', colspan: 1, accepted },
                { label: 'НПРЧ', colspan: 1, accepted },
                ...nestedSecond.map((el) => el),
                { label: 'мин', colspan: 1, accepted },
                { label: 'макс', colspan: 1, accepted },
                ...nestedThird.map((el) => el),
                { label: 'мин', colspan: 1, accepted },
                { label: 'макс', colspan: 1, accepted },
                ...nestedFourth.map((el) => el),
                { label: 'мин', colspan: 1, accepted },
                { label: 'макс', colspan: 1, accepted },
                { label: 'заяв', colspan: 1, accepted },
                ...nestedFifth.map((el) => el),
                { label: 'потр', colspan: 1, accepted },
              ],
              [
                {
                  label: PlanningStageRussia[columnStages['RESULT_MIN']],
                  colspan: 1,
                  accepted,
                  code: columnStages['RESULT_MIN'],
                },
                {
                  label: PlanningStageRussia[columnStages['P_GEN']],
                  colspan: 1,
                  accepted,
                  code: columnStages['P_GEN'],
                },
                {
                  label: PlanningStageRussia[columnStages['RESULT_MAX']],
                  colspan: 1,
                  accepted,
                  code: columnStages['RESULT_MAX'],
                },

                {
                  label: PlanningStageRussia[columnStages['RESERVES_MAX']],
                  colspan: 1,
                  accepted,
                  code: columnStages['RESERVES_MAX'],
                },
                {
                  label: PlanningStageRussia[columnStages['AVRCHM_LOAD']],
                  colspan: 1,
                  accepted,
                  code: columnStages['AVRCHM_LOAD'],
                },
                {
                  label: PlanningStageRussia[columnStages['NPRCH']],
                  colspan: 1,
                  accepted,
                  code: columnStages['NPRCH'],
                },

                {
                  label: PlanningStageRussia[columnStages['LIMIT_MIN']],
                  colspan: 1,
                  accepted,
                  code: columnStages['LIMIT_MIN'],
                },
                {
                  label: PlanningStageRussia[columnStages['LIMIT_MAX']],
                  colspan: 1,
                  accepted,
                  code: columnStages['LIMIT_MAX'],
                },

                {
                  label: PlanningStageRussia[columnStages['CM_P_MIN']],
                  colspan: 1,
                  accepted,
                  code: columnStages['CM_P_MIN'],
                },
                {
                  label: PlanningStageRussia[columnStages['CM_P_MAX']],
                  colspan: 1,
                  accepted,
                  code: columnStages['CM_P_MAX'],
                },

                {
                  label: PlanningStageRussia[columnStages['MODES_P_MIN']],
                  colspan: 1,
                  accepted,
                  code: columnStages['MODES_P_MIN'],
                },
                {
                  label: PlanningStageRussia[columnStages['MODES_P_MAX']],
                  colspan: 1,
                  accepted,
                  code: columnStages['MODES_P_MAX'],
                },
                {
                  label: PlanningStageRussia[columnStages['MODES_DECLARED']],
                  colspan: 1,
                  accepted,
                  code: columnStages['MODES_DECLARED'],
                },

                {
                  label: PlanningStageRussia[columnStages['CONSUMPT']],
                  colspan: 1,
                  accepted,
                  code: columnStages['CONSUMPT'],
                },
              ],
            ]
      setNestedHeadersUp(tempNestedHeadersUp)
      setCustomHeadersUp(() => {
        const arr = tempNestedHeadersUp[tempNestedHeadersUp?.length - 1] ?? []
        let res: IVaultCustomHeaders = []
        arr.forEach((el, index) => {
          const accepted = el?.accepted
          const className = accepted ? 'acceptedBgColor' : ''
          const stage = selectedStage === 'ACTUAL' ? actualStage.code : selectedStage
          const finished = stage !== el.code
          res = [
            ...res,
            {
              col: index,
              className,
              stageClassName: getColorStage(el.code, finished),
              thText: PlanningStageRussia[el.code],
            },
          ]
        })

        return res
      })
      if (rgus.length) {
        const rguCount = rgus.length
        setCollapsibleColumnsUp([
          // Блок "Итог" всегда начинается с 0
          { row: -4, col: getStationColumnIndex('RESULTS', 0, rguCount), collapsible: true },
          // Блок "Резервы"
          {
            row: -4,
            col: getStationColumnIndex('RESERVES', 0, rguCount),
            collapsible: true,
          },
          // Блок "Итог.ОГР"
          {
            row: -4,
            col: getStationColumnIndex('LIMITS', 0, rguCount),
            collapsible: true,
          },
          // Блок "РМ"
          {
            row: -4,
            col: getStationColumnIndex('CM', 0, rguCount),
            collapsible: true,
          },
          // Блок "Модес"
          {
            row: -4,
            col: getStationColumnIndex('MODES', 0, rguCount),
            collapsible: true,
          },
        ])
      } else {
        setCollapsibleColumnsUp([])
      }
      // Конфигурация колонок
      let columnFirst: ColumnSettings[] = disabled
        ? [
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
          ]
        : [
            { editor: false, readOnly: false },
            { editor: 'numeric', readOnly: false },
            { editor: false, readOnly: false },
          ]
      rgus.forEach(() => {
        columnFirst = disabled
          ? [
              ...columnFirst,
              { editor: false, readOnly: false },
              { editor: false, readOnly: false },
              { editor: false, readOnly: false },
            ]
          : [
              ...columnFirst,
              { editor: false, readOnly: false },
              { editor: 'numeric', readOnly: false },
              { editor: false, readOnly: false },
            ]
      })
      const columnSecondTmp: ColumnSettings[] = disabled
        ? [
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
          ]
        : [
            { editor: false, readOnly: false },
            FLOOD_MODE_WATCH ? { editor: false, readOnly: false } : { editor: 'numeric' },
            { editor: 'numeric' },
          ]

      let columnSecond = columnSecondTmp
      rgus.forEach(() => {
        columnSecond = [...columnSecond, ...columnSecondTmp]
      })

      let columnRmSettings: ColumnSettings[] = disabled
        ? [
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
          ]
        : [{ editor: 'numeric' }, { editor: 'numeric' }]
      rgus.forEach(() => {
        columnRmSettings = disabled
          ? [...columnRmSettings, { editor: false, readOnly: false }, { editor: false, readOnly: false }]
          : [...columnRmSettings, { editor: 'numeric' }, { editor: 'numeric' }]
      })

      let columnModesSettings: ColumnSettings[] = disabled
        ? [
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
            { editor: false, readOnly: false },
          ]
        : [{ editor: 'numeric' }, { editor: 'numeric' }, { editor: false, readOnly: false }]
      rgus.forEach(() => {
        columnModesSettings = disabled
          ? [
              ...columnModesSettings,
              { editor: false, readOnly: false },
              { editor: false, readOnly: false },
              { editor: false, readOnly: false },
            ]
          : [...columnModesSettings, { editor: 'numeric' }, { editor: 'numeric' }, { editor: false, readOnly: false }]
      })

      const columnTemp: ColumnSettings[] = [
        ...columnFirst, // Итог (мин, план, макс)
        ...columnSecond, // Резервы (Rмакс, АВРЧМ)
        ...columnRmSettings.map(() => ({ editor: false, readOnly: false })), // Итог.ОГР (мин, макс) СОЗДАТЬ СВОЮ ПЕРЕМЕННУЮ!!!
        ...columnRmSettings, // РМ (мин, макс)
        ...columnModesSettings, // Модес (мин, макс, заяв)
        { editor: false, readOnly: false }, // ИСП (потр)
      ]

      setColumnUp(columnTemp)
      let typeKeys: string[] = []
      let keysMap: string[] = []
      if (rgus.length > 0) {
        let one: string[] = []
        let two: string[] = []
        let three: string[] = []
        let four: string[] = []
        let five: string[] = []
        rgus.forEach((rgu: any) => {
          one = [...one, `${rgu.rguId}-P_MIN_RESULT`, `${rgu.rguId}-P_GEN`, `${rgu.rguId}-P_MAX_RESULT`]
          two = [...two, `${rgu.rguId}-RESERVES_MAX`, `${rgu.rguId}-AVRCHM_LOAD`, `${rgu.rguId}-NPRCH`]
          three = [...three, `${rgu.rguId}-P_MIN`, `${rgu.rguId}-P_MAX`]
          four = [...four, `${rgu.rguId}-CM_P_MIN`, `${rgu.rguId}-CM_P_MAX`]
          five = [...five, `${rgu.rguId}-MODES_P_MIN`, `${rgu.rguId}-MODES_P_MAX`, `${rgu.rguId}-MODES_DECLARED`]
        })
        keysMap = [
          'P_MIN_RESULT',
          'P_GEN',
          'P_MAX_RESULT',
          ...one,
          'RESERVES_MAX',
          'AVRCHM_LOAD',
          'NPRCH',
          ...two,
          'P_MIN',
          'P_MAX',
          ...three,
          'CM_P_MIN',
          'CM_P_MAX',
          ...four,
          'MODES_P_MIN',
          'MODES_P_MAX',
          'MODES_DECLARED',
          ...five,
          'CONSUMPT',
        ]
        typeKeys = [
          'STATION',
          'STATION',
          'STATION',
          ...one.map(() => `RGU`),
          'STATION',
          'STATION',
          'STATION',
          ...two.map(() => `RGU`),
          'STATION',
          'STATION',
          ...three.map(() => `RGU`),
          'STATION',
          'STATION',
          ...four.map(() => `RGU`),
          'STATION',
          'STATION',
          'STATION',
          ...five.map(() => `RGU`),
          'STATION',
        ]
      } else {
        keysMap = [...keysGES, 'CONSUMPT']
        typeKeys = [...keysGES, `STATION`].map(() => `STATION`)
      }
      setCellUp(() => {
        let res: CellSettings[] = []
        dataTemp.forEach((row, x) => {
          res = [
            ...res,
            ...row
              .map((_, y: number) => {
                const maxConsumptionHourTemp = maxConsumptionHour - 1
                const minConsumptionHourTemp = minConsumptionHour - 1
                const isMaxConsumptionHour =
                  maxConsumptionHourTemp === x - 1 ||
                  maxConsumptionHourTemp === x ||
                  (maxConsumptionHourTemp === x + 1 && maxConsumptionHourTemp !== -2)
                const isMinConsumptionHour =
                  minConsumptionHourTemp === x - 1 ||
                  minConsumptionHourTemp === x ||
                  (minConsumptionHourTemp === x + 1 && minConsumptionHourTemp !== -2)
                const isPGEN = y === 1
                const plantOptimizedFinal = plantOptimized ? 'plantOptimized' : 'plantNotOptimized'
                const typeCell = typeKeys[y]
                let object: ITempGesCell = {
                  P_MIN_RESULT: 0,
                  P_GEN: 0,
                  P_MAX_RESULT: 0,
                  RESERVES_MAX: 0,
                  AVRCHM_LOAD: 0,
                  NPRCH: 0,
                  P_MIN: 0,
                  P_MAX: 0,
                  CM_P_MIN: 0,
                  CM_P_MAX: 0,
                  MODES_P_MIN: 0,
                  MODES_P_MAX: 0,
                  MODES_DECLARED: 0,
                  CONSUMPT: 0,
                  allowedZones: [],
                }
                const cells = rows[x].cells
                let keyStation = ''
                if (typeCell === 'STATION') {
                  keyStation = keysMap[y]
                  const P_GEN = cells.find((item) => item.column === 'P_GEN')?.value ?? 0
                  const CM_P_MIN = cells.find((item) => item.column === 'CM_P_MIN') ?? undefined
                  const CM_P_MAX = cells.find((item) => item.column === 'CM_P_MAX') ?? undefined
                  const MODES_P_MIN = cells.find((item) => item.column === 'MODES_P_MIN') ?? undefined
                  const MODES_P_MAX = cells.find((item) => item.column === 'MODES_P_MAX') ?? undefined
                  const MODES_DECLARED = cells.find((item) => item.column === 'MODES_DECLARED') ?? undefined
                  const getPmin = () => {
                    if (CM_P_MIN && MODES_P_MIN) {
                      return Math.max(CM_P_MIN ? CM_P_MIN?.value : 0, MODES_P_MIN ? MODES_P_MIN?.value : 0)
                    }
                    if (!CM_P_MIN && !MODES_P_MIN) {
                      return undefined
                    }
                    if (!CM_P_MIN) {
                      return MODES_P_MIN?.value ?? 0
                    }
                    if (!MODES_P_MIN) {
                      return CM_P_MIN?.value ?? 0
                    }
                  }
                  const getPmax = () => {
                    if (CM_P_MAX && MODES_P_MAX) {
                      return Math.min(CM_P_MAX ? CM_P_MAX?.value : 0, MODES_P_MAX ? MODES_P_MAX?.value : 0)
                    }
                    if (!CM_P_MAX && !MODES_P_MAX) {
                      return undefined
                    }
                    if (!CM_P_MAX) {
                      return MODES_P_MAX?.value ?? 0
                    }
                    if (!MODES_P_MAX) {
                      return CM_P_MAX?.value ?? 0
                    }
                  }
                  const P_MIN = getPmin() === undefined ? 0 : getPmin()
                  const P_MAX = getPmax() === undefined ? 0 : getPmax()
                  const AVRCHM_LOAD = cells.find((item) => item.column === 'AVRCHM_LOAD')?.value ?? 0
                  const NPRCH = cells.find((item) => item.column === 'NPRCH')?.value ?? 0
                  const CONSUMPT = cells.find((item) => item.column === 'CONSUMPT')?.value ?? undefined
                  const getPmaxRes = () => {
                    if (FLOOD_MODE_WATCH) {
                      if (REGULATED_UNIT === 'PLANT') {
                        return Number(P_MAX) - Number(AVRCHM_LOAD)
                      } else {
                        return rgus.reduce(function (sum, cur) {
                          const rgu = cur.rows[x].cells
                          const CM_P_MAX = rgu.find((item) => item.column === 'CM_P_MAX') ?? undefined
                          const MODES_P_MAX = rgu.find((item) => item.column === 'MODES_P_MAX') ?? undefined
                          const AVRCHM_LOAD = rgu.find((item) => item.column === 'AVRCHM_LOAD')?.value ?? 0
                          const getPmax = () => {
                            if (CM_P_MAX && MODES_P_MAX) {
                              return Math.min(CM_P_MAX ? CM_P_MAX?.value : 0, MODES_P_MAX ? MODES_P_MAX?.value : 0)
                            }
                            if (!CM_P_MAX && !MODES_P_MAX) {
                              return undefined
                            }
                            if (!CM_P_MAX) {
                              return MODES_P_MAX?.value ?? 0
                            }
                            if (!MODES_P_MAX) {
                              return CM_P_MAX?.value ?? 0
                            }
                          }
                          const P_MAX = getPmax() === undefined ? 0 : getPmax()

                          return sum + (Number(P_MAX) - Number(AVRCHM_LOAD))
                        }, 0)
                      }
                    } else {
                      return Number(P_MAX) - Number(AVRCHM_LOAD)
                    }
                  }
                  const allowedZonesRes = allowedZones?.length > 0 ? allowedZones[x]?.zones : []
                  const P_MAX_RESULT = makePMax(allowedZonesRes, getPmaxRes())
                  const getPminRes = () => {
                    if (FLOOD_MODE_WATCH) {
                      if (REGULATED_UNIT === 'PLANT') {
                        return makePMin(allowedZonesRes, P_MAX_RESULT - 1)
                      } else {
                        const sumRgus = rgus.reduce(function (sum, cur) {
                          const rgu = cur.rows[x].cells
                          const CM_P_MAX = rgu.find((item) => item.column === 'CM_P_MAX')
                          const MODES_P_MAX = rgu.find((item) => item.column === 'MODES_P_MAX')
                          const AVRCHM_LOAD = rgu.find((item) => item.column === 'AVRCHM_LOAD')?.value ?? 0
                          const getPmax = () => {
                            if (CM_P_MAX && MODES_P_MAX) {
                              return Math.min(CM_P_MAX ? CM_P_MAX?.value : 0, MODES_P_MAX ? MODES_P_MAX?.value : 0)
                            }
                            if (!CM_P_MAX && !MODES_P_MAX) {
                              return undefined
                            }
                            if (!CM_P_MAX) {
                              return MODES_P_MAX?.value ?? 0
                            }
                            if (!MODES_P_MAX) {
                              return CM_P_MAX?.value ?? 0
                            }
                          }
                          const P_MAX = getPmax() === undefined ? 0 : getPmax()

                          return sum + (Number(P_MAX) - Number(AVRCHM_LOAD)) - 1
                        }, 0)

                        return makePMin(allowedZonesRes, sumRgus)
                      }
                    } else {
                      return makePMin(allowedZonesRes, Number(P_MIN) + Number(AVRCHM_LOAD))
                    }
                  }
                  const P_MIN_RESULT = Math.max(0, getPminRes())
                  const RESERVES_MAX = cells.find((item) => item.column === 'RESERVES_MAX')?.value ?? undefined
                  object = {
                    P_MIN_RESULT,
                    P_GEN,
                    P_MAX_RESULT,
                    RESERVES_MAX: RESERVES_MAX !== undefined ? RESERVES_MAX : '',
                    AVRCHM_LOAD,
                    NPRCH,
                    P_MIN,
                    P_MAX,
                    CM_P_MIN: CM_P_MIN === undefined ? undefined : CM_P_MIN?.value,
                    CM_P_MAX: CM_P_MAX === undefined ? undefined : CM_P_MAX?.value,
                    MODES_P_MIN: MODES_P_MIN === undefined ? undefined : MODES_P_MIN?.value,
                    MODES_P_MAX: MODES_P_MAX === undefined ? undefined : MODES_P_MAX?.value,
                    MODES_DECLARED: MODES_DECLARED === undefined ? undefined : MODES_DECLARED?.value,
                    CONSUMPT,
                    allowedZones: allowedZonesRes,
                  }
                } else {
                  const [id, key] = (keysMap[y] ?? '').split('-')
                  keyStation = key
                  const objRgu = rgus.find((el) => el.rguId === Number(id))
                  const cells = objRgu?.rows[x]?.cells ?? []
                  const P_GEN = cells.find((item) => item.column === 'P_GEN')?.value ?? 0
                  const CM_P_MIN = cells.find((item) => item.column === 'CM_P_MIN') ?? undefined
                  const CM_P_MAX = cells.find((item) => item.column === 'CM_P_MAX') ?? undefined
                  const MODES_P_MIN = cells.find((item) => item.column === 'MODES_P_MIN') ?? undefined
                  const MODES_P_MAX = cells.find((item) => item.column === 'MODES_P_MAX') ?? undefined
                  const MODES_DECLARED = cells.find((item) => item.column === 'MODES_DECLARED') ?? undefined
                  const CONSUMPT = cells.find((item) => item.column === 'CONSUMPT')?.value ?? undefined
                  const getPmin = () => {
                    if (CM_P_MIN && MODES_P_MIN) {
                      return Math.max(CM_P_MIN ? CM_P_MIN?.value : 0, MODES_P_MIN ? MODES_P_MIN?.value : 0)
                    }
                    if (!CM_P_MIN && !MODES_P_MIN) {
                      return undefined
                    }
                    if (!CM_P_MIN) {
                      return MODES_P_MIN?.value ?? 0
                    }
                    if (!MODES_P_MIN) {
                      return CM_P_MIN?.value ?? 0
                    }
                  }
                  const getPmax = () => {
                    if (CM_P_MAX && MODES_P_MAX) {
                      return Math.min(CM_P_MAX ? CM_P_MAX?.value : 0, MODES_P_MAX ? MODES_P_MAX?.value : 0)
                    }
                    if (!CM_P_MAX && !MODES_P_MAX) {
                      return undefined
                    }
                    if (!CM_P_MAX) {
                      return MODES_P_MAX?.value ?? 0
                    }
                    if (!MODES_P_MAX) {
                      return CM_P_MAX?.value ?? 0
                    }
                  }
                  const P_MIN = getPmin() === undefined ? 0 : getPmin()
                  const P_MAX = getPmax() === undefined ? 0 : getPmax()
                  const AVRCHM_LOAD = cells.find((item) => item.column === 'AVRCHM_LOAD')?.value ?? 0
                  const NPRCH = cells.find((item) => item.column === 'NPRCH')?.value ?? 0
                  const P_MAX_RESULT = Number(P_MAX) - Number(AVRCHM_LOAD)
                  const P_MIN_RESULT = FLOOD_MODE_WATCH
                    ? Math.max(0, P_MAX_RESULT - 1)
                    : Math.max(0, Number(P_MIN) + Number(AVRCHM_LOAD))
                  const RESERVES_MAX = cells.find((item) => item.column === 'RESERVES_MAX')?.value ?? undefined
                  const allowedZones = objRgu?.allowedZones[x]?.zones?.length > 0 ? objRgu?.allowedZones[x]?.zones : []
                  object = {
                    P_MIN_RESULT: makePMin(allowedZones, P_MIN_RESULT),
                    P_GEN,
                    P_MAX_RESULT: makePMax(allowedZones, P_MAX_RESULT),
                    RESERVES_MAX: RESERVES_MAX !== undefined ? RESERVES_MAX : '',
                    AVRCHM_LOAD,
                    NPRCH,
                    P_MIN,
                    P_MAX,
                    CM_P_MIN: CM_P_MIN === undefined ? undefined : CM_P_MIN?.value,
                    CM_P_MAX: CM_P_MAX === undefined ? undefined : CM_P_MAX?.value,
                    MODES_P_MIN: MODES_P_MIN === undefined ? undefined : MODES_P_MIN?.value,
                    MODES_P_MAX: MODES_P_MAX === undefined ? undefined : MODES_P_MAX?.value,
                    MODES_DECLARED: MODES_DECLARED === undefined ? undefined : MODES_DECLARED?.value,
                    CONSUMPT,
                    allowedZones,
                  }
                }
                let isValid = false
                const value =
                  keyStation === 'P_MIN_RESULT'
                    ? Math.max(0, object[keyStation])
                    : object[keyStation as keyof ITempGesCell]
                const P_GEN_PLANT = rows[x]?.cells?.find((el) => el.column === 'P_GEN')?.value ?? 0
                const P_GEN_RGUS = rgus.reduce((acc, cur) => {
                  const value = cur.rows[x].cells.find((el) => el.column === 'P_GEN')?.value ?? 0

                  return acc + value
                }, 0)
                const AVRCHM_LOAD_PLANT = rows[x]?.cells?.find((el) => el.column === 'AVRCHM_LOAD')?.value ?? 0
                const AVRCHM_LOAD_RGUS = rgus.reduce((acc, cur) => {
                  const value = cur.rows[x].cells.find((el) => el.column === 'AVRCHM_LOAD')?.value ?? 0

                  return acc + value
                }, 0)
                const P_MIN_PLANT = rows[x]?.cells?.find((el) => el.column === 'LIMIT_MIN')?.value ?? 0
                const P_MIN_RGUS = rgus.reduce((acc, cur) => {
                  const value = cur.rows[x].cells.find((el) => el.column === 'LIMIT_MIN')?.value ?? 0

                  return acc + value
                }, 0)
                const P_MAX_PLANT = rows[x]?.cells?.find((el) => el.column === 'LIMIT_MAX')?.value ?? 0
                const P_MAX_RGUS = rgus.reduce((acc, cur) => {
                  const value = cur.rows[x].cells.find((el) => el.column === 'LIMIT_MAX')?.value ?? 0

                  return acc + value
                }, 0)
                const CM_P_MIN_PLANT = rows[x]?.cells?.find((el) => el.column === 'CM_P_MIN')?.value ?? 0
                const CM_P_MIN_RGUS = rgus.reduce((acc, cur) => {
                  const value = cur.rows[x].cells.find((el) => el.column === 'CM_P_MIN')?.value ?? 0

                  return acc + value
                }, 0)
                const CM_P_MAX_PLANT = rows[x]?.cells?.find((el) => el.column === 'CM_P_MAX')?.value ?? 0
                const CM_P_MAX_RGUS = rgus.reduce((acc, cur) => {
                  const value = cur.rows[x].cells.find((el) => el.column === 'CM_P_MAX')?.value ?? 0

                  return acc + value
                }, 0)
                const IS_VIEW_CM_P_MIN = rgus.some((rgu) => {
                  return rgu.rows[x].cells.some((el) => el.column === 'CM_P_MIN')
                })
                const IS_VIEW_CM_P_MAX = rgus.some((rgu) => {
                  return rgu.rows[x].cells.some((el) => el.column === 'CM_P_MAX')
                })
                const validObject = {
                  is_Valid_P_GEN:
                    rgus.length > 0 ? Number(P_GEN_PLANT.toFixed(3)) !== Number(P_GEN_RGUS.toFixed(3)) : undefined,
                  is_Valid_AVRCHM_LOAD:
                    rgus.length > 0
                      ? Number(AVRCHM_LOAD_PLANT.toFixed(3)) !== Number(AVRCHM_LOAD_RGUS.toFixed(3))
                      : undefined,
                  is_Valid_P_MIN:
                    rgus.length > 0 ? Number(P_MIN_PLANT.toFixed(3)) < Number(P_MIN_RGUS.toFixed(3)) : undefined,
                  is_Valid_P_MAX:
                    rgus.length > 0 ? Number(P_MAX_PLANT.toFixed(3)) > Number(P_MAX_RGUS.toFixed(3)) : undefined,
                  is_Valid_CM_P_MIN: IS_VIEW_CM_P_MIN
                    ? Number(CM_P_MIN_PLANT !== undefined ? CM_P_MIN_PLANT.toFixed(3) : 0) <
                      Number(CM_P_MIN_RGUS !== undefined ? CM_P_MIN_RGUS.toFixed(3) : 0)
                    : undefined,
                  is_Valid_CM_P_MAX: IS_VIEW_CM_P_MAX
                    ? Number(CM_P_MAX_PLANT !== undefined ? CM_P_MAX_PLANT.toFixed(3) : 0) >
                      Number(CM_P_MAX_RGUS !== undefined ? CM_P_MAX_RGUS.toFixed(3) : 0)
                    : undefined,
                }
                const countKeys = keysMap[y]?.split('-').length ?? 1
                const typeObj = countKeys === 1 ? 'PLANT' : 'RGU'
                const VALIDATE_MAIN = getValidGES(
                  dataForStation.plantId,
                  keyStation,
                  object,
                  value,
                  inputResultProps[0],
                )
                const VALIDATE_PLANT_WITH_RGU = additionalValidation(
                  typeObj,
                  object,
                  value,
                  keyStation,
                  REGULATED_UNIT,
                  validObject,
                )
                let comment = ''
                isValid = VALIDATE_MAIN === undefined && VALIDATE_PLANT_WITH_RGU === undefined
                const VALIDATE_MESSAGE_MAIN = VALIDATE_MAIN === undefined ? '' : VALIDATE_MAIN
                const VALIDATE_MESSAGE_PLANT_WITH_RGU =
                  VALIDATE_PLANT_WITH_RGU === undefined ? '' : VALIDATE_PLANT_WITH_RGU
                if (!isValid) {
                  comment = VALIDATE_MESSAGE_MAIN + VALIDATE_MESSAGE_PLANT_WITH_RGU
                }
                const cell = getGesCellPropByTableCoords(rows, rgus, x, y)
                const renderer = getStatusCell(
                  isMaxConsumptionHour,
                  isMinConsumptionHour,
                  columnTemp[y]?.editor,
                  keyStation,
                  plantOptimizedFinal,
                  isValid,
                  cell?.manual || cell?.fixed,
                )

                return {
                  row: x,
                  col: y,
                  renderer,
                  isMaxConsumptionHour,
                  isMinConsumptionHour,
                  editor: columnTemp[y]?.editor,
                  keyStation,
                  isPGEN,
                  plantOptimizedFinal,
                  manual: cell?.manual,
                  fixed: cell?.fixed,
                  allowedZones: object.allowedZones,
                  comment: isValid ? null : getStyledComment(comment),
                } as CellSettings
              })
              .filter((el) => el.renderer !== 'cell'),
          ]
        })

        return res
      })
      if (FLOOD_MODE_WATCH) {
        const getDefaultPMAXSUM = rows.reduce(function (res, _, index) {
          const cells = rows[index]?.cells ?? []
          const CM_P_MAX = cells.find((item: any) => item.column === 'CM_P_MAX')
          const MODES_P_MAX = cells.find((item: any) => item.column === 'MODES_P_MAX')

          const getPmax = () => {
            if (CM_P_MAX && MODES_P_MAX) {
              return Math.min(CM_P_MAX.value, MODES_P_MAX?.value)
            }
            if (!CM_P_MAX && !MODES_P_MAX) {
              return undefined
            }
            if (!CM_P_MAX) {
              return MODES_P_MAX?.value ?? 0
            }
            if (!MODES_P_MAX) {
              return CM_P_MAX?.value ?? 0
            }
          }

          const pMax = getPmax()
          const temp = pMax === undefined ? 0 : pMax

          return res + temp
        }, 0)
        const getDefaultPMINSUM = rows.reduce(function (res, _, index) {
          const cells = rows[index]?.cells ?? []
          const CM_P_MAX = cells.find((item) => item.column === 'CM_P_MAX')
          const MODES_P_MAX = cells.find((item) => item.column === 'MODES_P_MAX')

          const getPmax = () => {
            if (CM_P_MAX && MODES_P_MAX) {
              return Math.min(CM_P_MAX ? CM_P_MAX?.value : 0, MODES_P_MAX ? MODES_P_MAX?.value : 0)
            }
            if (!CM_P_MAX && !MODES_P_MAX) {
              return undefined
            }
            if (!CM_P_MAX) {
              return MODES_P_MAX?.value ?? 0
            }
            if (!MODES_P_MAX) {
              return CM_P_MAX?.value ?? 0
            }
          }

          const pMax = getPmax()
          const temp = pMax === undefined ? 0 : pMax - 1

          return res + temp
        }, 0)
        const SUM_PMAX =
          rgus.length > 0
            ? rgus.reduce(function (res, cur) {
                const sumCurRgu = cur.rows.reduce(function (resRows, curRows) {
                  const cells = curRows?.cells ?? []
                  const CM_P_MAX = cells.find((item) => item.column === 'CM_P_MAX')
                  const MODES_P_MAX = cells.find((item) => item.column === 'MODES_P_MAX')
                  const getPmax = () => {
                    if (CM_P_MAX && MODES_P_MAX) {
                      return Math.min(CM_P_MAX ? CM_P_MAX?.value : 0, MODES_P_MAX ? MODES_P_MAX?.value : 0)
                    }
                    if (!CM_P_MAX && !MODES_P_MAX) {
                      return undefined
                    }
                    if (!CM_P_MAX) {
                      return MODES_P_MAX?.value ?? 0
                    }
                    if (!MODES_P_MAX) {
                      return CM_P_MAX?.value ?? 0
                    }
                  }
                  const pMax = getPmax()
                  const temp = pMax === undefined ? 0 : pMax

                  return resRows + temp
                }, 0)

                return res + sumCurRgu
              }, 0) / 1000
            : getDefaultPMAXSUM / 1000
        const SUM_PMIN =
          rgus.length > 0
            ? rgus.reduce(function (res, cur) {
                const sumCurRgu = cur.rows.reduce(function (resRows, curRows) {
                  const cells = curRows?.cells ?? []
                  const CM_P_MIN = cells.find((item) => item.column === 'CM_P_MIN')
                  const MODES_P_MIN = cells.find((item) => item.column === 'MODES_P_MIN')
                  const getPmin = () => {
                    if (CM_P_MIN && MODES_P_MIN) {
                      return Math.max(CM_P_MIN ? CM_P_MIN?.value : 0, MODES_P_MIN ? MODES_P_MIN?.value : 0)
                    }
                    if (!CM_P_MIN && !MODES_P_MIN) {
                      return undefined
                    }
                    if (!CM_P_MIN) {
                      return MODES_P_MIN?.value ?? 0
                    }
                    if (!MODES_P_MIN) {
                      return CM_P_MIN?.value ?? 0
                    }
                  }
                  const pMin = getPmin()
                  const temp = pMin === undefined ? 0 : pMin

                  return resRows + temp
                }, 0)

                return res + sumCurRgu
              }, 0) / 1000
            : getDefaultPMINSUM / 1000
        if (plantOptimized) {
          setInputValues({
            P_GEN_TARGET:
              inputValues?.P_GEN_TARGET?.value !== undefined
                ? roundingTo3DigitsEminEmaxPgen(inputValues?.P_GEN_TARGET?.value / 1000)
                : '',
            W_MIN: Number.isNaN(SUM_PMIN) ? '' : round(SUM_PMIN, 3),
            W_MAX: Number.isNaN(SUM_PMAX) ? '' : round(SUM_PMAX, 3),
            FLOOD_MODE_WATCH: inputValues?.FLOOD_MODE_WATCH?.value?.turnedOn ?? false,
          })
        } else {
          const W_MIN = getValueEminEmax('W_MIN', inputValues, E_MAX_E_MIN)
          const W_MAX = getValueEminEmax('W_MAX', inputValues, E_MAX_E_MIN)
          setInputValues({
            P_GEN_TARGET: round(Number.isNaN(SUM_PMAX) ? '' : SUM_PMAX, 3),
            W_MIN: W_MIN ? round(W_MIN, 3) : '',
            W_MAX: W_MAX ? round(W_MAX, 3) : '',
            FLOOD_MODE_WATCH: inputValues?.FLOOD_MODE_WATCH?.value?.turnedOn ?? false,
          })
        }
      } else {
        const W_MIN = getValueEminEmax('W_MIN', inputValues, E_MAX_E_MIN)
        const W_MAX = getValueEminEmax('W_MAX', inputValues, E_MAX_E_MIN)
        const is_W_MIN = W_MIN !== ''
        const is_W_MAX = W_MAX !== ''
        setInputValues({
          P_GEN_TARGET:
            inputValues?.P_GEN_TARGET?.value !== undefined
              ? round(roundingTo3DigitsEminEmaxPgen(inputValues?.P_GEN_TARGET?.value / 1000), 3)
              : '',
          W_MIN: is_W_MIN ? round(W_MIN, 3) : '',
          W_MAX: is_W_MAX ? round(W_MAX, 3) : '',
          FLOOD_MODE_WATCH: inputValues?.FLOOD_MODE_WATCH?.value?.turnedOn ?? false,
        })
      }
    }
    setTimeout(() => {
      setSignalUpdateChart(true)
    }, TIME_LOADER)
  }

  const loadDataStation = async (prepareDate: any, stage: any) => {
    initLoadDataAbortControllerRef.current?.abort(AbortRequestReason.REPEAT_REQUEST)
    initLoadDataAbortControllerRef.current = new AbortController()
    await loadDataForStation(selectLeftMenu, prepareDate, stage, initLoadDataAbortControllerRef.current)
      .then((props: any) => {
        refreshPlantsListVisuals() // Решает проблему с отображением акцептованных/неакцептованных станций в боковой панели
        prepareDataStation(props)
      })
      .then(() => {
        setTimeout(() => {
          if (isMounted.current) {
            setIsLoadingUp(false)
            setIsLoadingDown(false)
            resetDataForAside()
          }
        }, 0)
      })
  }

  const calculationEnteringAllowedZones = (
    type: string,
    date: Date,
    selectedStage: any,
    selectLeftMenu: any,
    calcEnteringAllowedZones: Function,
  ) => {
    const prepareDate = getPrepareDate(date)

    return calcEnteringAllowedZones(selectLeftMenu, prepareDate, selectedStage, type).then(() => {
      loadDataStation(prepareDate, selectedStage)
    })
  }

  const calculationAllowedZones = (date: Date, selectedStage: any, selectLeftMenu: any, calcAllowedZones: any) => {
    const prepareDate = getPrepareDate(date)

    return calcAllowedZones(selectLeftMenu, prepareDate, selectedStage).then(() => {
      loadDataStation(prepareDate, selectedStage)
    })
  }
  const calculationGeneration = (
    date: Date,
    selectedStage: any,
    selectLeftMenu: any,
    calcGeneration: Function,
    method?: TCalcGenerationMethod,
  ) => {
    const prepareDate = getPrepareDate(date)

    return calcGeneration(selectLeftMenu, prepareDate, selectedStage, method)
  }

  const handleCalculationGeneration = async (method?: TCalcGenerationMethod) => {
    const prepareDate = getPrepareDate(initDate())
    const stage = selectedStage === 'ACTUAL' ? actualStage.code : selectedStage

    try {
      await calculationGeneration(initDate(), stage, selectLeftMenu, calcGeneration, method)
      loadDataStation(prepareDate, stage)
    } catch (error) {
      console.error(error)
    }
  }

  const loadData = async () => {
    // setCustomHeadersUp([])
    const prepareDate = getPrepareDate(initDate())
    const stage = selectedStage === 'ACTUAL' ? actualStage.code : selectedStage
    await loadDataStation(prepareDate, stage)
  }

  const initLoadData = () => {
    if (stages.length > 0 && plantsListForAside.length > 1) {
      loadData().catch((e) => {
        console.log(e)
      })
    }
  }

  const isView = selectLeftMenu === 0 ? plantsListForAside.filter((el: any) => el.mixing).length > 0 : true

  const titleHeader = plantsListForAside.find((el: any) => el.value === selectLeftMenu)?.label ?? 'Свод'

  const prevDate = useRef()
  const prevSelectedStage = useRef()
  const prevGodMode = useRef()
  useEffect(() => {
    if (
      isStagesExist === StagesInitStatus.loadedWithData &&
      (dataForStation.plantId !== selectLeftMenu ||
        prevDate.current !== date ||
        prevSelectedStage.current !== selectedStage ||
        prevGodMode.current !== godMode)
    ) {
      prevDate.current = date
      prevSelectedStage.current = selectedStage
      prevGodMode.current = godMode
      initLoadData()
    }
  }, [isStagesExist, plantsListForAside, selectLeftMenu, dataForStation.plantId, selectedStage, date, godMode])

  const prepareDataForSave = () => {
    return new Promise((resolve: any) => {
      setDataUp((rows: any) => {
        return rows.map((row: any) => {
          return row.map((el: any) => {
            if (el === '-') {
              return ''
            }
            if (el === '-0') {
              return '0'
            }
            if (el === '-0.') {
              return '0'
            }
            if (el === '0.') {
              return '0'
            }

            return el
          })
        })
      })
      resolve()
    })
  }

  const inputs = dataForStation?.inputValues ?? {}

  const is_P_GEN_TARGET = Object.keys(inputs).some((el: any) => el === 'P_GEN_TARGET')
  const is_W_MIN = Object.keys(inputs).some((el: any) => el === 'W_MIN')
  const is_W_MAX = Object.keys(inputs).some((el: any) => el === 'W_MAX')
  const is_FLOOD_MODE_WATCH = Object.keys(inputs).some((el: any) => el === 'FLOOD_MODE_WATCH')
  const is_TERTIARY_RESERVE = Object.keys(inputs).some((el: any) => el === 'TERTIARY_RESERVE')

  const saveStation = (isMainSave: boolean) => {
    const tableColumnLength = hotUp?.getColHeader()?.length || 0

    return prepareDataForSave().then(() => {
      setSyncStatus('SAVE', TaskStatus.IN_PROCESS)
      const targetDate = getPrepareDate(initDate())
      const plantId = selectLeftMenu
      const inputValuesFinal: any = dataForStation?.inputValues
      if (is_P_GEN_TARGET) {
        const isNumber = !Number.isNaN(parseFloat(inputValues.P_GEN_TARGET))
        inputValuesFinal.P_GEN_TARGET.value = isNumber ? Number(inputValues.P_GEN_TARGET) * 1000 : undefined
      }
      if (is_W_MIN) {
        const isNumber = !Number.isNaN(parseFloat(inputValues.W_MIN))
        if (isNumber) {
          inputValuesFinal.W_MIN.value = Number(inputValues.W_MIN) * 1000 <= 0 ? 0 : Number(inputValues.W_MIN) * 1000
        } else {
          inputValuesFinal.W_MIN.value = undefined
        }
      }
      if (is_W_MAX) {
        const isNumber = !Number.isNaN(parseFloat(inputValues.W_MAX))
        if (isNumber) {
          inputValuesFinal.W_MAX.value = Number(inputValues.W_MAX) * 1000 <= 0 ? 0 : Number(inputValues.W_MAX) * 1000
        } else {
          inputValuesFinal.W_MAX.value = undefined
        }
      }
      if (is_FLOOD_MODE_WATCH) {
        inputValuesFinal.FLOOD_MODE_WATCH.value.turnedOn = inputValues.FLOOD_MODE_WATCH
      }

      // Добавляем в результирующий объект для сохранения данные из TextField "Третичного резерва"
      if (is_TERTIARY_RESERVE) {
        const terReserveValue = dataForStation.inputValues.TERTIARY_RESERVE?.value

        if (terReserveValue === '' || terReserveValue === null || terReserveValue === undefined) {
          inputValuesFinal.TERTIARY_RESERVE.value = undefined
        } else {
          const numericValue = parseFloat(terReserveValue)

          if (isNaN(numericValue)) {
            inputValuesFinal.TERTIARY_RESERVE.value = undefined
          } else {
            inputValuesFinal.TERTIARY_RESERVE.value = numericValue
          }
        }
      }

      const rgus = dataForStation.rgus
      let values: any = []
      let rguValues: any = rgus.map((el: any) => {
        return { rguId: el.rguId, values: [] as any[] }
      })
      const tableDataUp = hotUp?.getData()?.slice(0, 24) ?? []
      tableDataUp.map((row: any, hour: number) => {
        return row.map((el: any, index: number) => {
          if (index === 0) {
            values = enrichStationDataBeforeSave(cellUp, tableColumnLength, values, ['RESULT_MIN'], el, hour, index)
          }
          if (index === 1) {
            values = enrichStationDataBeforeSave(cellUp, tableColumnLength, values, ['P_GEN'], el, hour, index)
          }
          if (index === 2) {
            values = enrichStationDataBeforeSave(cellUp, tableColumnLength, values, ['RESULT_MAX'], el, hour, index)
          }
          if (index === getStationColumnIndex('RESERVES', COLUMN_POSITIONS.RESERVES_MAX, rgus.length)) {
            values = enrichStationDataBeforeSave(cellUp, tableColumnLength, values, ['RESERVES_MAX'], el, hour, index)
          }
          if (index === getStationColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, rgus.length)) {
            values = enrichStationDataBeforeSave(
              cellUp,
              tableColumnLength,
              values,
              ['AVRCHM_LOAD', 'AVRCHM_UNLOAD'],
              el,
              hour,
              index,
            )
          }
          if (index === getStationColumnIndex('RESERVES', COLUMN_POSITIONS.NPRCH, rgus.length)) {
            values = enrichStationDataBeforeSave(cellUp, tableColumnLength, values, ['NPRCH'], el, hour, index)
          }
          if (index === getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MIN, rgus.length)) {
            values = enrichStationDataBeforeSave(cellUp, tableColumnLength, values, ['LIMIT_MIN'], el, hour, index)
          }
          if (index === getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MAX, rgus.length)) {
            values = enrichStationDataBeforeSave(cellUp, tableColumnLength, values, ['LIMIT_MAX'], el, hour, index)
          }
          if (index === getStationColumnIndex('CM', COLUMN_POSITIONS.CM_P_MIN, rgus.length)) {
            values = enrichStationDataBeforeSave(cellUp, tableColumnLength, values, ['CM_P_MIN'], el, hour, index)
          }
          if (index === getStationColumnIndex('CM', COLUMN_POSITIONS.CM_P_MAX, rgus.length)) {
            values = enrichStationDataBeforeSave(cellUp, tableColumnLength, values, ['CM_P_MAX'], el, hour, index)
          }
          rguValues = rguValues.map((rgu: any, indexx: number) => {
            if (index === getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MIN_RESULT, indexx, rgus.length)) {
              return {
                ...rgu,
                values: enrichStationDataBeforeSave(
                  cellUp,
                  tableColumnLength,
                  rgu.values,
                  ['RESULT_MIN'],
                  el,
                  hour,
                  index,
                ),
              }
            }
            if (index === getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_GEN, indexx, rgus.length)) {
              return {
                ...rgu,
                values: enrichStationDataBeforeSave(cellUp, tableColumnLength, rgu.values, ['P_GEN'], el, hour, index),
              }
            }
            if (index === getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MAX_RESULT, indexx, rgus.length)) {
              return {
                ...rgu,
                values: enrichStationDataBeforeSave(
                  cellUp,
                  tableColumnLength,
                  rgu.values,
                  ['RESULT_MAX'],
                  el,
                  hour,
                  index,
                ),
              }
            }
            if (index === getRguColumnIndex('RESERVES', COLUMN_POSITIONS.RESERVES_MAX, indexx, rgus.length)) {
              return {
                ...rgu,
                values: enrichStationDataBeforeSave(
                  cellUp,
                  tableColumnLength,
                  rgu.values,
                  ['RESERVES_MAX'],
                  el,
                  hour,
                  index,
                ),
              }
            }
            if (index === getRguColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, indexx, rgus.length)) {
              return {
                ...rgu,
                values: enrichStationDataBeforeSave(
                  cellUp,
                  tableColumnLength,
                  rgu.values,
                  ['AVRCHM_LOAD', 'AVRCHM_UNLOAD'],
                  el,
                  hour,
                  index,
                ),
              }
            }
            if (index === getRguColumnIndex('RESERVES', COLUMN_POSITIONS.NPRCH, indexx, rgus.length)) {
              return {
                ...rgu,
                values: enrichStationDataBeforeSave(cellUp, tableColumnLength, rgu.values, ['NPRCH'], el, hour, index),
              }
            }
            if (index === getRguColumnIndex('LIMITS', COLUMN_POSITIONS.P_MIN, indexx, rgus.length)) {
              return {
                ...rgu,
                values: enrichStationDataBeforeSave(
                  cellUp,
                  tableColumnLength,
                  rgu.values,
                  ['LIMIT_MIN'],
                  el,
                  hour,
                  index,
                ),
              }
            }
            if (index === getRguColumnIndex('LIMITS', COLUMN_POSITIONS.P_MAX, indexx, rgus.length)) {
              return {
                ...rgu,
                values: enrichStationDataBeforeSave(
                  cellUp,
                  tableColumnLength,
                  rgu.values,
                  ['LIMIT_MAX'],
                  el,
                  hour,
                  index,
                ),
              }
            }
            if (index === getRguColumnIndex('CM', COLUMN_POSITIONS.CM_P_MIN, indexx, rgus.length)) {
              return {
                ...rgu,
                values: enrichStationDataBeforeSave(
                  cellUp,
                  tableColumnLength,
                  rgu.values,
                  ['CM_P_MIN'],
                  el,
                  hour,
                  index,
                ),
              }
            }
            if (index === getRguColumnIndex('CM', COLUMN_POSITIONS.CM_P_MAX, indexx, rgus.length)) {
              return {
                ...rgu,
                values: enrichStationDataBeforeSave(
                  cellUp,
                  tableColumnLength,
                  rgu.values,
                  ['CM_P_MAX'],
                  el,
                  hour,
                  index,
                ),
              }
            }

            return rgu
          })

          return undefined
        })
      })
      rguValues = rguValues.map((el: any) => {
        return {
          ...el,
          values: el.values.map(({ value, hour, column, fixed, manual }: any) => {
            if (value === null) {
              return { column, hour }
            }

            return { value: value, hour, column, fixed, manual }
          }),
        }
      })
      values = values.map(({ value, hour, column, fixed, manual }: any) => {
        if (value === null) {
          return { column, hour }
        }

        return { value, hour, column, fixed, manual }
      })

      const objectPost = {
        plantId,
        targetDate,
        planingStage: selectedStage === ACTUAL_ITEM.value ? actualStage?.code : selectedStage,
        values,
        inputValues: inputValuesFinal,
        rguValues,
      }

      return saveDataStation(objectPost, isMainSave)
        .then(async (isComplete: boolean) => {
          if (isComplete && isMainSave) {
            const prepareDate = getPrepareDate(initDate())
            const stage = selectedStage === 'ACTUAL' ? actualStage.code : selectedStage
            await loadDataStation(prepareDate, stage)
          } else if (!isComplete) {
            throw Error('Данные загружены с ошибкой')
          }
        })
        .finally(() => {
          setSyncStatus('SAVE', TaskStatus.DONE)
        })
    })
  }

  const resetDataForAside = () => {
    setIsInputParams(false)
    setEditCellsUp([])
  }

  const selectedCells = useRef<
    {
      row: number
      col: number
      row2: number
      col2: number
    }[]
  >([])
  const setSelectedCells = (cells: SpreadsheetSelectedCells[]) => {
    selectedCells.current = cells
  }

  /**
   * Сохранение допустимых зон по индексам выделенной ячейки.
   * Зоны уникальны в рамках строки для станции и РГЕ.
   * @param selectedRow - Индекс выбранной строки
   * @param selectedCol - Индекс выбранной ячейки
   */
  const afterSelectionUp: Handsontable.GridSettings['afterSelection'] = (selectedRow, selectedCol) => {
    // Обработка случая, когда пользователь выделяет ячейку в шапке или номер часа.
    // Индексы данных в таблице начинаются с 0 и увеличиваются в положительную сторону, а индексы шапки в отрицательную.
    if (selectedRow < 0 || selectedCol < 0) return
    // Строка больше 24 часа не содержит информации о допустимых зонах, поэтому исключаем их из обработки.
    if (selectedRow > 23) {
      return
    }
    // Общее кол-во колонок РГЕ для колонок типа «Итог»
    const lengthOfTotalNumberOfColumnsResultRgu = dataForStation.rgus.length * 3
    // Список допустимых зон для станции по часам
    const allowedZones = dataForStation?.allowedZones ?? []
    // Зоны для выбранного часа по станции
    setSelectedCellAllowedZones(allowedZones[selectedRow]?.zones ?? [])

    // Обработка выбранных зон для РГЕ
    if (dataForStation.rgus.length) {
      // Обработка колонки Итог, которая состоит из 3х колонок: мин, план, макс.
      // Индексы 0, 1, 2 - это индексы колонок с данными по станции, они обрабатывались ранее.
      // Индексы, которые попадают под условие (selectedCol > 2) - это индексы колонок РГЕ.
      // Индексы, которые попадают под условие (selectedCol - 2 <= lengthOfTotalNumberOfColumnsResultRge) - это верхнее ограничение для обработки колонок Итог.
      if (selectedCol > 2 && selectedCol - 2 <= lengthOfTotalNumberOfColumnsResultRgu) {
        // Обработка колонок Итог для РГЕ.
        // 2 - вычитаем 2 индекса, которые относятся к станциям.
        // 3 - колонки (мин, план, макс).
        // Вычитаем 1 для того, чтобы получить индекс РГЕ.
        const idxOfRguResultColumn = Math.ceil((selectedCol - 2) / 3) - 1
        const allowedZones = dataForStation?.rgus[idxOfRguResultColumn].allowedZones ?? []
        // Внутри списка допустимых зон для РГЕ выбираем нужную строку.
        setSelectedCellAllowedZones(allowedZones[selectedRow]?.zones ?? [])
      } else if (selectedCol > lengthOfTotalNumberOfColumnsResultRgu + 2) {
        // Обработка колонок РГЕ, за исключением колонки «Итог»
        // Кол-во колонок для типов «Резервы», «Итог.ОГР», «РМ», «Модес» равно 2
        const numberOfColumnsForRestColumnsExceptResult = 2

        // + numberOfColumnsForRestColumnsExceptResult и + numberOfColumnsForRestColumnsExceptResult нужны, чтобы учесть колонки, которые относятся к станциям
        const n =
          (selectedCol - (lengthOfTotalNumberOfColumnsResultRgu + numberOfColumnsForRestColumnsExceptResult) - 1) %
          (dataForStation.rgus.length * numberOfColumnsForRestColumnsExceptResult +
            numberOfColumnsForRestColumnsExceptResult)

        // n === 0 - колонка по станции
        if (n > 1) {
          // Вычисляем индекс РГЕ путем деления на 2 (кол-во колонок (мин, макс)) и вычитаем 1, чтобы найти стартовый индекс РГЕ
          const idxOfRguByRestColumnsExceptResult = Math.ceil((n - 1) / 2) - 1
          const allowedZones = dataForStation?.rgus[idxOfRguByRestColumnsExceptResult].allowedZones ?? []
          setSelectedCellAllowedZones(allowedZones[selectedRow]?.zones ?? [])
        }
      }
    }
  }

  const [outsideTableClickCounter, setOutsideTableClickCounter] = useState<number>(0)
  const afterSelectionEndUp: Handsontable.GridSettings['afterSelectionEnd'] = () => {
    handleSelectedCoords(hotUp, setSelectedCells, setOutsideTableClickCounter)
  }

  const setFixingValues = () => {
    setOutsideTableClickCounter(0)
    if (selectedCells.current.length === 0 || outsideTableClickCounter > 1) {
      selectedCells.current = []

      return
    }
    const tableColumnLength = hotUp?.getColHeader()?.length || 0
    const cellUpTmp = [...cellUp]
    selectedCells.current.forEach((selectedCell) => {
      const { row, col, row2, col2 } = selectedCell
      for (let x = row; x <= row2; x++) {
        for (let y = col; y <= col2; y++) {
          const cellUpIdx = x * tableColumnLength + y
          const cell = cellUpTmp[cellUpIdx]
          if (cell?.editor === 'numeric') {
            const renderer = updateManualAdjustmentStatusForCell(cell.renderer, true)
            cellUpTmp[cellUpIdx]['fixed'] = true
            cellUpTmp[cellUpIdx]['renderer'] = renderer
            setIsInputParams(true)
          }
        }
      }
    })
    setCellUp(cellUpTmp)
    selectedCells.current = []
  }

  const resetFixingValues = () => {
    setOutsideTableClickCounter(0)
    if (selectedCells.current.length === 0 || outsideTableClickCounter > 1) {
      selectedCells.current = []

      return
    }
    const tableColumnLength = hotUp?.getColHeader()?.length || 0
    const cellUpTmp = [...cellUp]
    selectedCells.current.forEach((selectedCell) => {
      const { row, col, row2, col2 } = selectedCell
      for (let x = row; x <= row2; x++) {
        for (let y = col; y <= col2; y++) {
          const cellUpIdx = x * tableColumnLength + y
          const cell = cellUpTmp[cellUpIdx]
          if (cell?.editor === 'numeric') {
            const renderer = updateManualAdjustmentStatusForCell(cell.renderer, false)
            cellUpTmp[cellUpIdx]['fixed'] = undefined
            cellUpTmp[cellUpIdx]['manual'] = undefined
            cellUpTmp[cellUpIdx]['renderer'] = renderer
            setIsInputParams(true)
          }
        }
      }
    })
    setCellUp(cellUpTmp)
    selectedCells.current = []
  }

  const handleSpreadsheetClickOutside = () => {
    setOutsideTableClickCounter((prev) => ++prev)
  }

  const afterDeselectUp = () => {
    setSelectedCellAllowedZones([])
  }

  const resetData = () => {
    calculationsPageStore.resetData()
    resetDataForAside()
    prepareDataStation(dataForStationOriginal)
    selectedCells.current = []
  }

  const acceptStation = () => {
    setIsAccepted(dataForStation.accepted)
  }

  const handleInputChange = useCallback(
    (value: string, key: string, status: any) => {
      if (!isLoadingUp) {
        setIsInputParams(true)
        if (key === 'FLOOD_MODE_WATCH') {
          const W_MIN = status
            ? hotUp?.getData()?.slice(24, 25)[0][0]
            : round(parseFloat(dataForStation?.inputValues?.W_MIN?.value) / 1000, 3)
          const W_MAX = status
            ? hotUp?.getData()?.slice(24, 25)[0][2]
            : round(parseFloat(dataForStation?.inputValues?.W_MAX?.value) / 1000, 3)
          setInputValues((prev: any) => ({
            ...prev,
            FLOOD_MODE_WATCH: status,
            W_MIN: W_MIN,
            W_MAX: W_MAX,
          }))
          updateDataStation(status, -1, -1, -1)
        } else if (key === 'W_MAX') {
          const isCalcWmin = dataForStation?.inputValues?.W_MIN?.calculate ?? false
          setInputValues((prev: any) => {
            const inputValues = {
              W_MAX: {
                calculate: dataForStation.inputValues?.W_MAX?.calculate,
                value: !Number.isNaN(parseFloat(value)) ? round(parseFloat(value) * 1000, 3) : undefined,
              },
              W_MIN: {
                calculate: dataForStation.inputValues?.W_MIN?.calculate,
                value: round(prev['W_MIN'], 3),
              },
            }
            const W_MIN = getValueEminEmax('W_MIN', inputValues, dataForStation?.E_MAX_E_MIN)

            return {
              ...prev,
              [key]: String(value)
                .split('')
                .filter((el) => el !== '-')
                .join(''),
              W_MIN: isCalcWmin
                ? W_MIN !== ''
                  ? round(W_MIN, 3)
                  : ''
                : prev['W_MIN'] !== ''
                  ? round(prev['W_MIN'], 3)
                  : '',
            }
          })
        } else {
          setInputValues((prev: any) => ({
            ...prev,
            [key]: String(value)
              .split('')
              .filter((el) => el !== '-')
              .join(''),
          }))
        }
      }
    },
    [dataForStation, isLoadingUp],
  )

  const inputResultProps: IInputResultItemProp[] = useMemo(() => {
    const rowResultData = hotUp?.getDataAtRow(24) ?? []

    const E_MAX_MIN = dataForStation?.emaxemin?.value ? dataForStation?.emaxemin?.value / 1000 : null
    const inputValuesCalc = {
      W_MIN: {
        calculate: dataForStation?.emaxemin?.turnedOn ?? false,
        value: inputValues?.W_MIN * 1000,
      },
      W_MAX: {
        value: inputValues?.W_MAX * 1000,
      },
    }

    const W_MIN =
      !Number.isNaN(Number(inputValues?.W_MIN)) && inputValues?.W_MIN !== ''
        ? inputValues?.FLOOD_MODE_WATCH
          ? rowResultData[0]
          : dataForStation?.emaxemin?.turnedOn
            ? getValueEminEmax('W_MIN', inputValuesCalc, E_MAX_MIN)
            : inputValues?.W_MIN
        : undefined
    const W_MAX =
      !Number.isNaN(Number(inputValues?.W_MAX)) && inputValues?.W_MAX !== ''
        ? inputValues?.FLOOD_MODE_WATCH
          ? rowResultData[2]
          : inputValues?.W_MAX
        : undefined
    const P_GEN_TARGET = inputValues?.FLOOD_MODE_WATCH ? rowResultData[2] : inputValues?.P_GEN_TARGET

    const inputValuesValidate = {
      W_MIN,
      W_MAX,
      P_GEN_TARGET: is_P_GEN_TARGET ? P_GEN_TARGET : undefined,
    }

    let isErrorPGENTARGET
    let isErrorWMIN
    let isErrorWMAX
    if (hotUp) {
      isErrorPGENTARGET = validateDailyOutputPlan(
        hotUp,
        inputValuesValidate,
        { pMin: 0, pGen: 1, pMax: 2 },
        is_P_GEN_TARGET,
        dataForStation.rgus.length ?? 0,
      )
      isErrorWMIN = validateDailyOutputMin(
        hotUp,
        inputValuesValidate,
        { pMin: 0, pGen: 1, pMax: 2 },
        is_W_MIN,
        dataForStation.rgus.length ?? 0,
      )
      isErrorWMAX = validateDailyOutputMax(
        hotUp,
        inputValuesValidate,
        { pMin: 0, pGen: 1, pMax: 2 },
        is_W_MAX,
        dataForStation.rgus.length ?? 0,
      )
    }

    return [
      {
        stationColNum: customHeadersUp[2]?.length || 0,
        wMin: {
          active: is_W_MIN,
          disabled:
            dataForStation?.accepted ||
            viewOnly ||
            isLastDay ||
            inputValues?.FLOOD_MODE_WATCH ||
            dataForStation?.emaxemin?.turnedOn ||
            !editMode ||
            isFinishStage,
          value: W_MIN,
          isValid: typeof isErrorWMIN !== 'string',
          comment: isErrorWMIN !== undefined ? getStyledComment(isErrorWMIN) : undefined,
        },
        pGen: {
          active: is_P_GEN_TARGET,
          disabled:
            dataForStation?.accepted ||
            viewOnly ||
            isLastDay ||
            inputValues?.FLOOD_MODE_WATCH ||
            !editMode ||
            isFinishStage,
          value: P_GEN_TARGET,
          isValid: typeof isErrorPGENTARGET !== 'string',
          comment: isErrorPGENTARGET !== undefined ? getStyledComment(isErrorPGENTARGET) : undefined,
        },
        wMax: {
          active: is_W_MAX,
          disabled:
            dataForStation?.accepted ||
            viewOnly ||
            isLastDay ||
            inputValues?.FLOOD_MODE_WATCH ||
            !editMode ||
            isFinishStage,
          value: W_MAX,
          isValid: typeof isErrorWMAX !== 'string',
          comment: isErrorWMAX !== undefined ? getStyledComment(isErrorWMAX) : undefined,
        },
      },
    ]
  }, [dataForStation, inputValues, customHeadersUp, viewOnly, isLastDay, editMode, isFinishStage])

  const formatBottomRowHeaders: GridSettings['afterGetRowHeader'] = (row, th) => {
    if (row === 24) th.classList.add(cls.bold)
  }

  const columnForValidation = useMemo(() => {
    const columnsError = cellUp.reduce(
      (acc, cell) => {
        if (!acc[cell.col]) {
          acc[cell.col] = cell.renderer.includes('isNotValid')
        }

        return acc
      },
      Array.from({ length: columnUp.length }),
    )
    inputResultProps.forEach((inputResultProp) => {
      columnsError[0] = columnsError[0] || !inputResultProp.wMin.isValid
      columnsError[1] = columnsError[1] || !inputResultProp.pGen.isValid
      columnsError[2] = columnsError[2] || !inputResultProp.wMax.isValid
    })
    if (columnUp.length && cellUp.length) {
      return columnUp.map((column, idx) => {
        return {
          ...column,
          plantOptimized: cellUp[idx]['plantOptimizedFinal'].includes('plantOptimized'),
          hasError: columnsError[idx],
        }
      })
    }

    return []
  }, [columnUp, cellUp, inputResultProps])

  const nestedHeaderClassNameMap: MapOfNestedHeadersBasedOnConfig = useMemo(() => {
    if (!nestedHeadersUp.length || !columnForValidation.length) return {}

    return getMapOfNestedHeadersBasedOnConfig(
      nestedHeadersUp,
      columnForValidation,
      dataForStation.rgus.length > 0 ? rguStationHeaderClassNameConfig : stationHeaderClassNameConfig,
    )
  }, [nestedHeadersUp, columnForValidation, cellUp])

  const handleAfterGetColHeader: Handsontable.GridSettings['afterGetColHeader'] = (col, th, level) => {
    const totalLevels: number = nestedHeadersUp.length
    const isLastLevel = level === totalLevels - 1

    if (isLastLevel && col === -1) {
      th.innerHTML = `<div class="relative"><span class="colHeader">Час</span></div>`

      return
    }
    const find = customHeadersUp.find((el) => el.col === col)
    !!find?.className && th.classList.add(find.className)
    if (isLastLevel) {
      if (find.stageClassName && find.thText) {
        th.classList.add(find.stageClassName)
        th.innerHTML = `<div style="display: flex;justify-content: center;align-items: center;width: 100%;height: 96%;">${find.thText}</div>`
      }
    } else {
      th.classList.add(cls.bold)
    }
    const classNames: string[] | undefined = nestedHeaderClassNameMap[`${level}-${col}`]
    if (classNames?.length > 0) {
      th.classList.add(...classNames)
    }
  }

  const generationUpContainer = () => {
    if (isLoadingUp || dataForStation.plantId === null) {
      return (
        <div className={cls.Loader}>
          <Loader />
        </div>
      )
    }

    return (
      <div
        onClick={() => {
          if (!validateDate(dateISP)) {
            const { year = null, month = null, day = null } = locationParse(location.search)
            const dateISPInit = dataForStation.ISP_DATE
              ? getIspDate(dataForStation.ISP_DATE)
              : year && month && day
                ? new Date(`${year}-${month}-${day}`)
                : new Date()
            setDateISP(dateISPInit)
          }
        }}
      >
        <ClickAwayListener onClickAway={handleSpreadsheetClickOutside}>
          <Spreadsheet
            hasConsumpt
            height={dataForStation.rgus.length > 0 ? '496px' : '479px'}
            viewOnly={viewOnly || !editMode || isLastDay || dataForStation?.accepted || isFinishStage}
            className='spreadsheetSm'
            afterDeselect={afterDeselectUp}
            afterSelection={afterSelectionUp}
            afterSelectionEnd={afterSelectionEndUp}
            setHot={setHotUp}
            afterChange={afterChangeUp}
            cell={cellUp}
            columns={columnUp}
            nestedHeaders={nestedHeadersUp}
            collapsibleColumns={collapsibleColumnsUp}
            data={dataUp}
            customHeaders={customHeadersUp}
            inputResultChange={handleInputChange}
            inputResultProps={inputResultProps}
            afterGetRowHeader={formatBottomRowHeaders}
            afterGetColHeader={handleAfterGetColHeader}
            beforeCancelChange={beforeCancelChange}
          />
        </ClickAwayListener>
      </div>
    )
  }

  const generationDownContainer = () => {
    if (isLoadingDown) {
      return (
        <div className={cls.Loader}>
          <Loader />
        </div>
      )
    }
    if (!isView) {
      return <></>
    }

    return (
      <>
        <PlannedValuesChart
          columnUp={columnUp}
          handsontable={hotUp}
          isLoadingDown={isLoadingDown}
          signalUpdateChart={signalUpdateChart}
          setSignalUpdateChart={setSignalUpdateChart}
        />
        <ActualValuesChart classes={cls.telemetryGraph} />
      </>
    )
  }

  const save = async () => {
    try {
      await saveStation(true)
      resetDataForAside()
      selectedCells.current = []
    } catch (error) {
      console.error('Ошибка сохранения:', error)
    }
  }

  const handleModesWrite = async () => {
    const params = {
      plantId: selectLeftMenu,
      targetDate: getPrepareDate(initDate()),
      planingStage:
        selectedStage === 'ACTUAL'
          ? stages?.find((el: any) => {
              return el.value === actualStage.code
            })?.value
          : selectedStage,
    }

    try {
      calculationsPageStore.updateCalcPossibilityTaskStatus({
        status: TaskStatus.IN_PROCESS,
        type: { code: CalculationTaskType.WRITE_PLAN_RGU_DATA },
      })
      await api.calcModelManager.writePlanRguData(params)

      notificationStore.addNotification({
        title: 'Загрузка данных ',
        description: 'Загрузка планового графика в Модес началась',
        timer: 6000,
        type: 'success',
      })
    } catch (error) {
      calculationsPageStore.updateCalcPossibilityTaskStatus({
        status: TaskStatus.FAILED,
        type: { code: CalculationTaskType.WRITE_PLAN_RGU_DATA },
      })
      console.log(error)
    }
  }

  const [isVisibleModalHistoryModes, setIsVisibleModalHistoryModes] = useState(false)
  const handleOpenModalHistoryModes = () => {
    setIsVisibleModalHistoryModes(true)
  }

  const [isModalInit, setIsModalInit] = useState(false)

  const inizialitionCalc = async () => {
    const targetDate = getPrepareDate(initDate())
    const planingStage = selectedStage === 'ACTUAL' ? actualStage.code : selectedStage
    const params = {
      targetDate,
      planingStage,
      plantId: dataForStation.plantId,
    }

    try {
      setSyncStatus('INITIALIZE', TaskStatus.IN_PROCESS)

      await inizCalc(params)

      setIsLoadingUp(true)
      setIsLoadingDown(true)

      await loadDataStation(targetDate, planingStage)
    } catch (error) {
      console.error('Ошибка во время инициализации:', error)
      throw error
    } finally {
      setSyncStatus('INITIALIZE', TaskStatus.DONE)
    }
  }

  const getButtons = (key: MessagesWarnings) => {
    switch (key) {
      case MessagesWarnings.SET_FIXING_VALUES:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.SET_FIXING_VALUES}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={setFixingValues}
          />
        )
      case MessagesWarnings.RESET_FIXING_VALUES:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.RESET_FIXING_VALUES}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={resetFixingValues}
          />
        )
      case MessagesWarnings.DO_OPTIMIZATION:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.DO_OPTIMIZATION}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={async () => {
              const prepareDate = getPrepareDate(initDate())
              const stage = selectedStage === 'ACTUAL' ? actualStage.code : selectedStage
              await calculationsPageStore
                .calcOptimization({
                  plantId: dataForStation.plantId,
                  planingStage: stage,
                  targetDate: prepareDate,
                })
                .catch((e) => {
                  console.log(e)
                })
            }}
          />
        )
      case MessagesWarnings.CALCULATE_ALLOWED_ZONES:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.CALCULATE_ALLOWED_ZONES}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={() => {
              return calculationAllowedZones(initDate(), selectedStage, selectLeftMenu, calcAllowedZones).catch((e) => {
                console.log(e)
              })
            }}
          />
        )
      case MessagesWarnings.CALCULATE_GENERATION:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.CALCULATE_GENERATION}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={() => handleCalculationGeneration()}
          />
        )
      case MessagesWarnings.CALCULATE_GENERATION_MAXIMUM:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.CALCULATE_GENERATION_MAXIMUM}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={() => handleCalculationGeneration('MAXIMUM')}
          />
        )
      case MessagesWarnings.ENTERING_ALLOWED_ZONES:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.ENTERING_ALLOWED_ZONES}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={async () => {
              setIsLoadingUp(true)
              setIsLoadingDown(true)
              await calculationEnteringAllowedZones(
                'CONSUMPTION_SCHEDULE_CHANGE',
                initDate(),
                selectedStage,
                selectLeftMenu,
                calcEnteringAllowedZones,
              ).catch((e) => {
                console.log(e)
              })
            }}
          />
        )
      case MessagesWarnings.ENTERING_ALLOWED_ZONES_TO_BOUNDS:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.ENTERING_ALLOWED_ZONES_TO_BOUNDS}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={async () => {
              await calculationEnteringAllowedZones(
                'ROUND_TO_BOUND',
                initDate(),
                selectedStage,
                selectLeftMenu,
                calcEnteringAllowedZones,
              ).catch((e) => {
                console.log(e)
              })
            }}
          />
        )
      case MessagesWarnings.BALANCE_RGU:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.BALANCE_RGU}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={async () => {
              const prepareDate = getPrepareDate(initDate())
              const stage = selectedStage === 'ACTUAL' ? actualStage.code : selectedStage
              await calcRGE(selectLeftMenu, prepareDate, stage).then(() => {
                loadDataStation(prepareDate, stage)
              })
            }}
          />
        )
      case MessagesWarnings.LOAD_ALL:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.LOAD_ALL}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={() => {
              downloadTheSourceData(
                null,
                selectedStage,
                actualStage,
                initDate(),
                selectLeftMenu,
                onDownloadTheSourceData,
                dateISP,
              )
            }}
          />
        )
      case MessagesWarnings.LOAD_ISP:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.LOAD_ISP}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={() => {
              downloadTheSourceData(
                'LOAD_CONSUMPTION',
                selectedStage,
                actualStage,
                initDate(),
                selectLeftMenu,
                onDownloadTheSourceData,
                dateISP,
              )
            }}
          />
        )
      case MessagesWarnings.LOAD_MODES:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.LOAD_MODES}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={() => {
              downloadTheSourceData(
                'LOAD_PLANT_DATA',
                selectedStage,
                actualStage,
                initDate(),
                selectLeftMenu,
                onDownloadTheSourceData,
                dateISP,
              )
            }}
          />
        )
      case MessagesWarnings.LOAD_CM:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.LOAD_CM}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={() => {
              downloadTheSourceData(
                'LOAD_CM_DATA',
                selectedStage,
                actualStage,
                initDate(),
                selectLeftMenu,
                onDownloadTheSourceData,
                dateISP,
              )
            }}
          />
        )
      case MessagesWarnings.WRITE_MODES:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.WRITE_MODES}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={handleModesWrite}
          />
        )
      case MessagesWarnings.RESET:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.RESET}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={resetData}
          />
        )
      case MessagesWarnings.SAVE:
        return <StationSaveButton isEditRows={isEditRows} isLastDay={isLastDay} viewOnly={viewOnly} onClick={save} />
      case MessagesWarnings.UPDATE_ACCEPT:
        return (
          <StationAcceptButton
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            onClick={acceptStation}
          />
        )
      case MessagesWarnings.UPDATE_ACCEPT_CANCEL:
        return (
          <StationDisacceptButton
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            onClick={acceptStation}
          />
        )
      case MessagesWarnings.INITIALIZE:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.INITIALIZE}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={() => setIsModalInit(true)}
          />
        )
      case MessagesWarnings.HISTORY_ACCEPT:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.HISTORY_ACCEPT}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={() => {
              setIsHistoryModal({
                name: titleHeader,
                data: dataForStation,
              })
            }}
          />
        )
      case MessagesWarnings.HISTORY_MODES:
        return (
          <CalculationActionButton
            buttonName={MessagesWarnings.HISTORY_MODES}
            viewOnly={viewOnly}
            isEditRows={isEditRows}
            isLastDay={isLastDay}
            editMode={editMode}
            onClick={handleOpenModalHistoryModes}
          />
        )
      default:
        return <></>
    }
  }
  const disabledDateIsp = viewOnly || isEditRows || isLastDay || dataForStation?.accepted || isFinishStage

  return (
    <>
      <div className={cls.main}>
        <SubtitleWithActions
          isActionsVisible
          title={
            <div className={cls.HeaderCustomWithButton}>
              <AccessControl rules={[ROLES.TECHNOLOGIST]}>{getButtons(MessagesWarnings.INITIALIZE)}</AccessControl>
              <h2 className={cls.TitleStation}>{titleHeader}</h2>
            </div>
          }
          className={cls.title}
          actions={[
            <AccessControl rules={[ROLES.TECHNOLOGIST]} key='actions'>
              <div className={classNames(cls.ButtonContainer, {}, [cls.dashedBorder])}>
                {getButtons(MessagesWarnings.SET_FIXING_VALUES)}
                {getButtons(MessagesWarnings.RESET_FIXING_VALUES)}
              </div>
              <div className={classNames(cls.ButtonContainer, {}, [cls.dashedBorder])}>
                {getButtons(MessagesWarnings.LOAD_ALL)}
                {getButtons(MessagesWarnings.LOAD_ISP)}
                {getButtons(MessagesWarnings.LOAD_MODES)}
                {getButtons(MessagesWarnings.LOAD_CM)}
              </div>
              <div className={classNames(cls.ButtonContainer, {}, [cls.dashedBorder])}>
                {isOptimization && getButtons(MessagesWarnings.DO_OPTIMIZATION)}
                {getButtons(MessagesWarnings.CALCULATE_ALLOWED_ZONES)}
                {getButtons(MessagesWarnings.CALCULATE_GENERATION)}
                {getButtons(MessagesWarnings.ENTERING_ALLOWED_ZONES)}
                {getButtons(MessagesWarnings.ENTERING_ALLOWED_ZONES_TO_BOUNDS)}
                {getButtons(MessagesWarnings.BALANCE_RGU)}
                {getButtons(MessagesWarnings.CALCULATE_GENERATION_MAXIMUM)}
              </div>
              <div className={classNames(cls.ButtonModesContainer, {}, [cls.dashedBorder])}>
                {getButtons(MessagesWarnings.WRITE_MODES)}
                {getButtons(MessagesWarnings.HISTORY_MODES)}
              </div>
              {editMode ? (
                <>
                  {getButtons(MessagesWarnings.RESET)}
                  {getButtons(MessagesWarnings.SAVE)}
                </>
              ) : (
                <>
                  <div className={cls.ButtonSave}></div>
                  <div className={cls.ButtonSave}></div>
                </>
              )}
              <>
                {getButtons(MessagesWarnings.UPDATE_ACCEPT)}
                {getButtons(MessagesWarnings.UPDATE_ACCEPT_CANCEL)}
                {getButtons(MessagesWarnings.HISTORY_ACCEPT)}
                <ExportReport
                  selectedPlant={plantsListForAside.find((el: ICalcModelPlantListItem) => el.value === selectLeftMenu)}
                  plantType={dataForStation.plantType}
                  planningStage={dataForStation.planingStage}
                  regulatedUnit={dataForStation.REGULATED_UNIT}
                  disabled={isEditRows}
                  message={isEditRows && 'Включён режим редактирования'}
                />
                <ButtonMailingReport plantId={dataForStation.plantId} />
              </>
            </AccessControl>,
          ]}
        />
        <StationHeader
          isFloodModeWatch={is_FLOOD_MODE_WATCH}
          viewOnly={viewOnly}
          isLastDay={isLastDay}
          dataForStation={dataForStation}
          inputValues={inputValues}
          onChange={handleInputChange}
          dateISP={dateISP}
          setDateISP={setDateISP}
          disabledDateIsp={disabledDateIsp}
          editMode={editMode}
          roles={accessRole}
          updateFloodWatch={updateFloodWatch}
          setIsInputParams={setIsInputParams}
        />
        <div
          ref={refUp}
          className={classNames(
            cls.Up,
            {
              [cls.stationRgusFullWidth]: !!dataForStation?.rgus.length,
              [cls.stationFullWidth]: !dataForStation?.rgus.length,
            },
            [],
          )}
        >
          <div style={{ width: widthUp * 0.66, height: heightUp }}>{generationUpContainer()}</div>
          <div
            style={{
              width: widthUp * 0.34,
              height: heightUp,
            }}
          >
            <TelemetryTable
              plantId={selectLeftMenu}
              classes={cls.telemetry}
              height={heightUp}
              isRgu={dataForStation.rgus.length > 0}
              editMode={editMode}
              isLoadingTelemetry={isLoadingTelemetryInProcess}
              selectedDate={telemetryTableDate}
              setSelectedDate={setTelemetryTableDate}
              getTelemetry={getTelemetry}
              loadTelemetryByDate={getTelemetryByDate}
              telemetryTableData={telemetryTableData}
            />
          </div>
        </div>
        <div className={classNames(cls.Middle)}>
          <Zones />
        </div>
        <div ref={refDown} className={classNames(cls.Down, {}, [cls.fullWidth])}>
          <div className={cls.DownContainer}>{generationDownContainer()}</div>
        </div>
      </div>
      {isConfirmModal && <ConfirmModal {...isConfirmModal} onClose={() => setIsConfirmModal(null)} />}
      {acceptedErrors.length > 0 && (
        <AcceptErrorModal
          onClose={() => resetAcceptErrors()}
          onConfirm={async () => {
            setSyncStatus('UPDATE_ACCEPT', TaskStatus.IN_PROCESS)
            try {
              await handleAcceptObject(
                initDate(),
                selectedStage,
                setAcceptObject,
                selectLeftMenu,
                setIsAccepted,
                initLoadData,
                true,
                setPlantsListForAside,
                plantsListForAside,
              )
            } catch (error) {
              console.error('Ошибка при выполнении операции:', error)
            } finally {
              setSyncStatus('UPDATE_ACCEPT', TaskStatus.DONE)
            }
          }}
        />
      )}
      {isAccepted !== null && (
        <AcceptModal
          onClose={() => {
            setIsAccepted(null)
          }}
          accept={isAccepted}
          onConfirm={async () => {
            try {
              if (!isAccepted) {
                setSyncStatus('UPDATE_ACCEPT', TaskStatus.IN_PROCESS)
                await handleAcceptObject(
                  initDate(),
                  selectedStage,
                  setAcceptObject,
                  selectLeftMenu,
                  setIsAccepted,
                  initLoadData,
                  false,
                  setPlantsListForAside,
                  plantsListForAside,
                )
              } else {
                setSyncStatus('UPDATE_ACCEPT_CANCEL', TaskStatus.IN_PROCESS)
                await handleDisacceptObject(
                  initDate(),
                  selectedStage,
                  selectLeftMenu,
                  setDisacceptObject,
                  setIsAccepted,
                  initLoadData,
                  setPlantsListForAside,
                  plantsListForAside,
                )
              }
            } catch (error) {
              console.error('Ошибка при выполнении операции:', error)
            } finally {
              setSyncStatus('UPDATE_ACCEPT', TaskStatus.DONE)
              setSyncStatus('UPDATE_ACCEPT_CANCEL', TaskStatus.DONE)
            }
          }}
        />
      )}
      {isHistoryModal && (
        <HistoryAcceptModal
          object={isHistoryModal}
          onClose={() => {
            setIsHistoryModal(null)
          }}
        />
      )}
      {isModalInit && (
        <ModalInit
          onClose={() => setIsModalInit(false)}
          onConfirm={async () => {
            try {
              await inizialitionCalc()
              setIsModalInit(false)
            } catch (error) {
              console.error('Произошла ошибка при инициализации', error)
            }
          }}
        />
      )}
      {isVisibleModalHistoryModes && (
        <ModalHistoryModes
          subtitle={`${plantsListForAside.find((el: any) => el.value === selectLeftMenu).label}, этап планирования ${
            selectedStage === 'ACTUAL'
              ? stages?.find((el: any) => {
                  return el.value === actualStage.code
                })?.label
              : stages?.find((el: any) => {
                  return el.value === selectedStage
                })?.label
          }`}
          plantId={selectLeftMenu}
          calcDate={getPrepareDate(initDate())}
          planingStage={
            selectedStage === 'ACTUAL'
              ? stages?.find((el: any) => {
                  return el.value === actualStage.code
                })?.value
              : selectedStage
          }
          onClose={() => setIsVisibleModalHistoryModes(false)}
        />
      )}
    </>
  )
})
