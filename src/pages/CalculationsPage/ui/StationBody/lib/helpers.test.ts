import { describe, expect, it } from 'vitest'

import { COLUMN_BLOCK_SIZES, COLUMN_POSITIONS, getRguColumnIndex, getStationColumnIndex } from './helpers'

describe('Column Index Calculations', () => {
  const rguCount = 2 // Тестируем с 2 РГУ

  describe('getStationColumnIndex', () => {
    it('should calculate correct indices for RESULTS block', () => {
      expect(getStationColumnIndex('RESULTS', COLUMN_POSITIONS.P_MIN_RESULT, rguCount)).toBe(0)
      expect(getStationColumnIndex('RESULTS', COLUMN_POSITIONS.P_GEN, rguCount)).toBe(1)
      expect(getStationColumnIndex('RESULTS', COLUMN_POSITIONS.P_MAX_RESULT, rguCount)).toBe(2)
    })

    it('should calculate correct indices for RESERVES block', () => {
      // RESERVES блок начинается после RESULTS: 3 * (2 + 1) = 9 колонок
      expect(getStationColumnIndex('RESERVES', COLUMN_POSITIONS.RESERVES_MAX, rguCount)).toBe(9)
      expect(getStationColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, rguCount)).toBe(10)
      expect(getStationColumnIndex('RESERVES', COLUMN_POSITIONS.NPRCH, rguCount)).toBe(11)
    })

    it('should calculate correct indices for LIMITS block', () => {
      // LIMITS блок начинается после RESULTS + RESERVES: 3*(2+1) + 3*(2+1) = 18 колонок
      expect(getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MIN, rguCount)).toBe(18)
      expect(getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MAX, rguCount)).toBe(19)
    })
  })

  describe('getRguColumnIndex', () => {
    it('should calculate correct indices for first RGU in RESULTS block', () => {
      // Первая РГУ в RESULTS: станция занимает 0,1,2, РГУ начинается с 3
      expect(getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MIN_RESULT, 0, rguCount)).toBe(3)
      expect(getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_GEN, 0, rguCount)).toBe(4)
      expect(getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MAX_RESULT, 0, rguCount)).toBe(5)
    })

    it('should calculate correct indices for second RGU in RESULTS block', () => {
      // Вторая РГУ в RESULTS: станция 0,1,2 + первая РГУ 3,4,5 + вторая РГУ начинается с 6
      expect(getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MIN_RESULT, 1, rguCount)).toBe(6)
      expect(getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_GEN, 1, rguCount)).toBe(7)
      expect(getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_MAX_RESULT, 1, rguCount)).toBe(8)
    })

    it('should calculate correct indices for RGU in RESERVES block with NPRCH', () => {
      // RESERVES блок начинается с индекса 9, первая РГУ начинается с 12
      expect(getRguColumnIndex('RESERVES', COLUMN_POSITIONS.RESERVES_MAX, 0, rguCount)).toBe(12)
      expect(getRguColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, 0, rguCount)).toBe(13)
      expect(getRguColumnIndex('RESERVES', COLUMN_POSITIONS.NPRCH, 0, rguCount)).toBe(14)
    })

    it('should calculate correct indices for RGU in LIMITS block', () => {
      // LIMITS блок начинается с индекса 18, первая РГУ начинается с 20
      expect(getRguColumnIndex('LIMITS', COLUMN_POSITIONS.P_MIN, 0, rguCount)).toBe(20)
      expect(getRguColumnIndex('LIMITS', COLUMN_POSITIONS.P_MAX, 0, rguCount)).toBe(21)
    })
  })

  describe('Block sizes validation', () => {
    it('should have correct block sizes', () => {
      expect(COLUMN_BLOCK_SIZES.RESULTS).toBe(3)
      expect(COLUMN_BLOCK_SIZES.RESERVES).toBe(3)
      expect(COLUMN_BLOCK_SIZES.LIMITS).toBe(2)
      expect(COLUMN_BLOCK_SIZES.CM).toBe(2)
      expect(COLUMN_BLOCK_SIZES.MODES).toBe(3)
    })
  })

  describe('Column positions validation', () => {
    it('should have correct NPRCH position in RESERVES block', () => {
      expect(COLUMN_POSITIONS.RESERVES_MAX).toBe(0)
      expect(COLUMN_POSITIONS.AVRCHM_LOAD).toBe(1)
      expect(COLUMN_POSITIONS.NPRCH).toBe(2) // NPRCH должна быть третьей колонкой
    })
  })

  describe('FLOOD_MODE_WATCH formula validation', () => {
    it('should use correct column indices for P_MAX and AVRCHM_LOAD in flood mode', () => {
      // В режиме половодья формула должна быть: MAX(0, P_MAX - AVRCHM_LOAD)
      // P_MAX находится в блоке LIMITS, позиция 1
      // AVRCHM_LOAD находится в блоке RESERVES, позиция 1

      const pMaxStationIndex = getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MAX, rguCount)
      const avrchmStationIndex = getStationColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, rguCount)

      // Для 2 РГУ: LIMITS блок начинается с индекса 18, P_MAX имеет позицию 1
      expect(pMaxStationIndex).toBe(19) // 18 + 1
      // RESERVES блок начинается с индекса 9, AVRCHM_LOAD имеет позицию 1
      expect(avrchmStationIndex).toBe(10) // 9 + 1
    })

    it('should validate flood mode logic for station results', () => {
      // В режиме половодья для станции:
      // P_MIN_RESULT = P_MAX_RESULT - 1
      // P_GEN = P_MAX_RESULT
      // P_MAX_RESULT = MAX(0, P_MAX - AVRCHM_LOAD)

      // Проверяем, что индексы колонок правильно рассчитываются
      const pMinResultIndex = getStationColumnIndex('RESULTS', COLUMN_POSITIONS.P_MIN_RESULT, rguCount)
      const pGenIndex = getStationColumnIndex('RESULTS', COLUMN_POSITIONS.P_GEN, rguCount)
      const pMaxResultIndex = getStationColumnIndex('RESULTS', COLUMN_POSITIONS.P_MAX_RESULT, rguCount)

      expect(pMinResultIndex).toBe(0)
      expect(pGenIndex).toBe(1)
      expect(pMaxResultIndex).toBe(2)
    })

    it('should calculate correct AVRCHM_LOAD index for updateFloodWatch', () => {
      // При переключении режима половодья через updateFloodWatch
      // нужно правильно определить индекс колонки AVRCHM_LOAD для обнуления

      const avrchmStationIndex = getStationColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, rguCount)

      // Для 2 РГУ: RESERVES блок начинается с индекса 9, AVRCHM_LOAD имеет позицию 1
      expect(avrchmStationIndex).toBe(10) // 9 + 1

      // Проверяем, что AVRCHM_LOAD не путается с NPRCH (позиция 2)
      const nprchStationIndex = getStationColumnIndex('RESERVES', COLUMN_POSITIONS.NPRCH, rguCount)
      expect(nprchStationIndex).toBe(11) // 9 + 2
      expect(avrchmStationIndex).not.toBe(nprchStationIndex)
    })
  })
})
